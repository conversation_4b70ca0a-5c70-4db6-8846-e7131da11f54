/**
 * 🌋 Volcano App Mobile - User Profile Component
 * Componente para mostrar y editar el perfil del usuario
 */

import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  TextInput,
  TouchableOpacity,
} from 'react-native';

import { useAuth } from '@/hooks/useAuth';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useI18n } from '@/hooks/useI18n';

import { AccessibleText } from '@/components/ui/AccessibleText';
import { PrimaryButton, SecondaryButton } from '@/components/ui/AccessibleButton';
import { ModernCard } from '@/components/ui/ModernCard';
import { ModernIcon } from '@/components/ui/ModernIcon';

import { Colors } from '@/constants/Colors';
import { BorderRadius, Spacing } from '@/constants/Layout';

interface UserProfileProps {
  onClose?: () => void;
}

export function UserProfile({ onClose }: UserProfileProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t } = useI18n();
  const { user, updateProfile, logout, isLoading } = useAuth();

  // Estados del formulario
  const [isEditing, setIsEditing] = useState(false);
  const [formData, setFormData] = useState({
    name: user?.name || '',
    phone: user?.phone || '',
  });
  const [isUpdating, setIsUpdating] = useState(false);

  const updateFormData = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSave = async () => {
    try {
      setIsUpdating(true);

      const success = await updateProfile({
        name: formData.name.trim(),
        phone: formData.phone.trim() || undefined,
      });

      if (success) {
        setIsEditing(false);
        Alert.alert(
          t('profile.success.title'),
          t('profile.success.updated')
        );
      } else {
        Alert.alert(
          t('profile.error.title'),
          t('profile.error.updateFailed')
        );
      }
    } catch (error) {
      console.error('❌ Profile update error:', error);
      Alert.alert(
        t('profile.error.title'),
        t('profile.error.networkError')
      );
    } finally {
      setIsUpdating(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      name: user?.name || '',
      phone: user?.phone || '',
    });
    setIsEditing(false);
  };

  const handleLogout = () => {
    Alert.alert(
      t('profile.logout.title'),
      t('profile.logout.message'),
      [
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
        {
          text: t('profile.logout.confirm'),
          style: 'destructive',
          onPress: async () => {
            const success = await logout();
            if (success && onClose) {
              onClose();
            }
          },
        },
      ]
    );
  };

  if (!user) {
    return (
      <ModernCard style={styles.container}>
        <View style={styles.emptyState}>
          <ModernIcon
            name="user-x"
            size={48}
            color={colors.textSecondary}
            style={styles.emptyIcon}
          />
          <AccessibleText
            variant="body"
            color="secondary"
            style={styles.emptyText}
          >
            {t('profile.notAuthenticated')}
          </AccessibleText>
        </View>
      </ModernCard>
    );
  }

  return (
    <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
      {/* Header */}
      <ModernCard style={styles.headerCard}>
        <View style={styles.header}>
          <View style={styles.avatarContainer}>
            <View style={[styles.avatar, { backgroundColor: colors.tint + '20' }]}>
              <ModernIcon
                name="user"
                size={32}
                color={colors.tint}
              />
            </View>
          </View>
          
          <View style={styles.userInfo}>
            <AccessibleText
              variant="title"
              style={[styles.userName, { color: colors.text }]}
            >
              {user.name || t('profile.noName')}
            </AccessibleText>
            <AccessibleText
              variant="body"
              color="secondary"
              style={styles.userEmail}
            >
              {user.email}
            </AccessibleText>
            <AccessibleText
              variant="bodySmall"
              color="muted"
              style={styles.memberSince}
            >
              {t('profile.memberSince', {
                date: new Date(user.created_at).toLocaleDateString()
              })}
            </AccessibleText>
          </View>

          {onClose && (
            <TouchableOpacity
              onPress={onClose}
              style={styles.closeButton}
            >
              <ModernIcon
                name="x"
                size={24}
                color={colors.icon}
              />
            </TouchableOpacity>
          )}
        </View>
      </ModernCard>

      {/* Profile Form */}
      <ModernCard style={styles.formCard}>
        <View style={styles.formHeader}>
          <AccessibleText
            variant="subtitle"
            style={[styles.formTitle, { color: colors.text }]}
          >
            {t('profile.personalInfo')}
          </AccessibleText>
          
          {!isEditing && (
            <TouchableOpacity
              onPress={() => setIsEditing(true)}
              style={styles.editButton}
            >
              <ModernIcon
                name="edit-2"
                size={20}
                color={colors.tint}
              />
            </TouchableOpacity>
          )}
        </View>

        {/* Name Field */}
        <View style={styles.fieldGroup}>
          <AccessibleText
            variant="button"
            style={[styles.fieldLabel, { color: colors.text }]}
          >
            {t('profile.fields.name')}
          </AccessibleText>
          
          {isEditing ? (
            <View style={[styles.inputContainer, { borderColor: colors.border }]}>
              <TextInput
                style={[styles.input, { color: colors.text }]}
                value={formData.name}
                onChangeText={(value) => updateFormData('name', value)}
                placeholder={t('profile.placeholders.name')}
                placeholderTextColor={colors.textSecondary}
                autoCapitalize="words"
                autoCorrect={false}
              />
            </View>
          ) : (
            <AccessibleText
              variant="body"
              style={[styles.fieldValue, { color: colors.text }]}
            >
              {user.name || t('profile.notSet')}
            </AccessibleText>
          )}
        </View>

        {/* Phone Field */}
        <View style={styles.fieldGroup}>
          <AccessibleText
            variant="button"
            style={[styles.fieldLabel, { color: colors.text }]}
          >
            {t('profile.fields.phone')}
          </AccessibleText>
          
          {isEditing ? (
            <View style={[styles.inputContainer, { borderColor: colors.border }]}>
              <TextInput
                style={[styles.input, { color: colors.text }]}
                value={formData.phone}
                onChangeText={(value) => updateFormData('phone', value)}
                placeholder={t('profile.placeholders.phone')}
                placeholderTextColor={colors.textSecondary}
                keyboardType="phone-pad"
              />
            </View>
          ) : (
            <AccessibleText
              variant="body"
              style={[styles.fieldValue, { color: colors.text }]}
            >
              {user.phone || t('profile.notSet')}
            </AccessibleText>
          )}
        </View>

        {/* Email Field (Read-only) */}
        <View style={styles.fieldGroup}>
          <AccessibleText
            variant="button"
            style={[styles.fieldLabel, { color: colors.text }]}
          >
            {t('profile.fields.email')}
          </AccessibleText>
          <AccessibleText
            variant="body"
            style={[styles.fieldValue, { color: colors.textSecondary }]}
          >
            {user.email}
          </AccessibleText>
          <AccessibleText
            variant="bodySmall"
            color="muted"
            style={styles.fieldNote}
          >
            {t('profile.emailNote')}
          </AccessibleText>
        </View>

        {/* Action Buttons */}
        {isEditing && (
          <View style={styles.actionButtons}>
            <SecondaryButton
              onPress={handleCancel}
              style={styles.cancelButton}
              disabled={isUpdating}
            >
              {t('common.cancel')}
            </SecondaryButton>
            
            <PrimaryButton
              onPress={handleSave}
              style={styles.saveButton}
              disabled={isUpdating || isLoading}
            >
              {isUpdating ? t('profile.saving') : t('common.save')}
            </PrimaryButton>
          </View>
        )}
      </ModernCard>

      {/* Logout Button */}
      <ModernCard style={styles.logoutCard}>
        <SecondaryButton
          onPress={handleLogout}
          style={[styles.logoutButton, { borderColor: colors.error }]}
          disabled={isLoading}
        >
          <View style={styles.logoutContent}>
            <ModernIcon
              name="log-out"
              size={20}
              color={colors.error}
              style={styles.logoutIcon}
            />
            <AccessibleText
              variant="button"
              style={[styles.logoutText, { color: colors.error }]}
            >
              {t('profile.logout.button')}
            </AccessibleText>
          </View>
        </SecondaryButton>
      </ModernCard>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  headerCard: {
    marginBottom: Spacing.md,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    marginRight: Spacing.md,
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  userInfo: {
    flex: 1,
  },
  userName: {
    marginBottom: Spacing.xs,
  },
  userEmail: {
    marginBottom: Spacing.xs,
  },
  memberSince: {
    fontStyle: 'italic',
  },
  closeButton: {
    padding: Spacing.sm,
  },
  formCard: {
    marginBottom: Spacing.md,
  },
  formHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.lg,
  },
  formTitle: {},
  editButton: {
    padding: Spacing.sm,
  },
  fieldGroup: {
    marginBottom: Spacing.lg,
  },
  fieldLabel: {
    marginBottom: Spacing.xs,
  },
  fieldValue: {
    fontSize: 16,
  },
  fieldNote: {
    marginTop: Spacing.xs,
    fontStyle: 'italic',
  },
  inputContainer: {
    borderWidth: 1,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    height: 48,
    justifyContent: 'center',
  },
  input: {
    fontSize: 16,
    height: '100%',
  },
  actionButtons: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: Spacing.lg,
  },
  cancelButton: {
    flex: 1,
    marginRight: Spacing.sm,
  },
  saveButton: {
    flex: 1,
    marginLeft: Spacing.sm,
  },
  logoutCard: {},
  logoutButton: {
    borderWidth: 1,
  },
  logoutContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  logoutIcon: {
    marginRight: Spacing.sm,
  },
  logoutText: {},
  emptyState: {
    alignItems: 'center',
    padding: Spacing.xl,
  },
  emptyIcon: {
    marginBottom: Spacing.md,
  },
  emptyText: {
    textAlign: 'center',
  },
});
