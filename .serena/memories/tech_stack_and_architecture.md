# Tech Stack & Architecture

## Core Technology Stack

### Framework & Platform
- **React Native 0.79.5** - Cross-platform mobile development
- **Expo ~53.0.20** - Development platform and build tooling
- **TypeScript ~5.8.3** - Static typing with strict mode enabled
- **Target Platforms**: iOS, Android, Web

### Navigation & Routing
- **Expo Router v5** - File-based routing system
- **React Navigation** - Native navigation integration
- **Structure**: Tab-based navigation with stack navigation support

### State Management
- **React Query (@tanstack/react-query)** - Server state management, caching, offline support
- **React Hooks** - Local state management
- **Context API** - Theme and global state

### Backend Integration
- **Supabase** - Database, real-time subscriptions, authentication
- **Axios** - HTTP client for API communication
- **WebSocket (socket.io-client)** - Real-time volcano data updates
- **Push Notifications** - expo-notifications for emergency alerts

### UI/UX & Design System
- **Custom Design System** - AccessibleText, AccessibleButton, ModernCard components
- **Lucide React Native** - Modern icon system
- **Expo Linear Gradient** - Modern gradient system
- **React Native Safe Area Context** - Proper safe area handling
- **WCAG AA Compliance** - Full accessibility support

### Location & Maps
- **Expo Location** - GPS and location services
- **Custom Interactive Map** - WebView-based mapping solution
- **React Native WebView** - Map rendering and interaction

### Data Visualization
- **React Native Chart Kit** - Volcano precursor data visualization
- **React Native SVG** - Custom charts and graphics

### Development Tools
- **Jest** - Testing framework with React Test Renderer
- **Babel** - JavaScript compilation with module resolver
- **TypeScript** - Type checking and IntelliSense
- **Expo Dev Tools** - Development server and debugging

## Architecture Patterns

### File-based Routing Structure
```
app/
├── _layout.tsx          # Root layout (QueryProvider, SafeAreaProvider)
├── +not-found.tsx       # 404 error page
└── (tabs)/             # Tab navigation group
    ├── _layout.tsx     # Tab layout with HapticTab
    ├── index.tsx       # Home dashboard
    ├── explore.tsx     # Interactive map
    └── precursors.tsx  # Volcano analysis
```

### Component Organization
```
components/
├── ui/                 # Design system components
│   ├── AccessibleText.tsx
│   ├── AccessibleButton.tsx
│   ├── ModernCard.tsx
│   ├── HeroCard.tsx
│   ├── ActionGrid.tsx
│   ├── ModernHeader.tsx
│   └── ModernIcon.tsx
└── [feature]/          # Domain-specific components
    ├── VolcanoStatus.tsx
    ├── InteractiveMap.tsx
    ├── AlertSystem.tsx
    └── ConnectionStatus.tsx
```

### Service Layer
```
services/
├── api.ts              # Axios HTTP client
├── websocket.ts        # Real-time data
├── notifications.ts    # Push notifications
├── supabase.ts        # Database integration
└── deviceId.ts        # Anonymous tracking
```

### Configuration & Constants
```
constants/
├── Colors.ts          # Theme system with gradients
├── Layout.ts          # Spacing, typography scales
└── Typography.ts      # Font system
```

## Import Path Configuration
- **Alias**: `@/*` points to project root
- **Path Resolution**: Configured in tsconfig.json and babel.config.js
- **Import Order**: External → Internal → Relative imports

## Development Patterns
- **TypeScript Strict Mode** - Full type safety
- **Functional Components** - React hooks pattern
- **Error Boundaries** - Comprehensive error handling
- **Offline-First** - React Query background sync
- **Platform-Specific Code** - Platform.select() for adaptations
- **Accessibility-First** - WCAG compliance throughout

## Performance Optimizations
- **React Query Caching** - Intelligent data caching and background updates
- **Lazy Loading** - Code splitting for large components
- **Image Optimization** - Expo Image for optimized loading
- **Background Sync** - Offline capability with sync on reconnect
- **Memoization** - React.memo and useMemo for expensive operations