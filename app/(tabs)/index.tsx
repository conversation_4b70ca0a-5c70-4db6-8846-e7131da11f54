/**
 * Volcano App - Dashboard Principal
 * Pantalla de inicio con estado del volcán y acceso rápido a funciones críticas
 */

import { ConnectionStatus } from '@/components/ConnectionStatus';
import {
    EmergencyButton,
    PrimaryButton,
    SecondaryButton
} from '@/components/ui/AccessibleButton';
import { AccessibleText } from '@/components/ui/AccessibleText';
import { HeroCard } from '@/components/ui/HeroCard';
import { ModernCard } from '@/components/ui/ModernCard';
import { ModernHeader } from '@/components/ui/ModernHeader';
import { ActionGrid, ActionItem } from '@/components/ui/ActionGrid';
import { LanguageSelector } from '@/components/ui/LanguageSelector';
import { UserHeader } from '@/components/UserHeader';
import { useI18n } from '@/hooks/useI18n';
import {
    AlertIcon,
    BellIcon,
    InfoIcon,
    MapIcon,
    SettingsIcon,
    VolcanoIcon,
} from '@/components/ui/ModernIcon';
import { LiveVolcanoStatus } from '@/components/VolcanoStatus';
import { AlertLevel, Colors } from '@/constants/Colors';
import { BorderRadius, Spacing } from '@/constants/Layout';
import { useVolcanoData } from '@/hooks/useApi';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useNotifications } from '@/hooks/useNotifications';
import { useWebSocket } from '@/services/websocket';
import { router } from 'expo-router';
import React, { useState } from 'react';
import {
    Alert,
    RefreshControl,
    ScrollView,
    StyleSheet,
    View
} from 'react-native';

// Mock data - En producción esto vendría de APIs oficiales
const mockVolcanoData = {
  name: 'Volcán Villarrica',
  alertLevel: 'ADVISORY' as AlertLevel,
  lastUpdate: new Date(Date.now() - 15 * 60 * 1000), // 15 minutos atrás
  description: 'Se observa actividad sísmica moderada y emisiones de gases normales.',
  location: 'Pucón, Región de la Araucanía',
  elevation: '2,847 metros',
};

const mockAlerts = [
  {
    id: 1,
    type: 'info',
    title: 'Actualización de Monitoreo',
    message: 'SERNAGEOMIN reporta actividad normal en las últimas 24 horas.',
    time: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 horas atrás
  },
  {
    id: 2,
    type: 'warning',
    title: 'Recomendación Turística',
    message: 'Se recomienda mantenerse informado antes de realizar actividades en el volcán.',
    time: new Date(Date.now() - 6 * 60 * 60 * 1000), // 6 horas atrás
  },
];

export default function HomeScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t } = useI18n();
  const [refreshing, setRefreshing] = useState(false);
  const [volcanoData, setVolcanoData] = useState(mockVolcanoData);
  const [alerts, setAlerts] = useState(mockAlerts);

  // API hooks
  const { isLoading, hasError, isOnline, refetchAll } = useVolcanoData();
  const { isConnected, lastEvent } = useWebSocket();
  const { state: notificationState, isReady: notificationsReady } = useNotifications();

  // Actualizar datos desde la API
  const onRefresh = async () => {
    setRefreshing(true);
    try {
      await refetchAll();
    } catch (error) {
      console.error('Error refreshing data:', error);
    } finally {
      setRefreshing(false);
    }
  };

  // Manejar botón de emergencia
  const handleEmergencyAction = () => {
    Alert.alert(
      t('emergency.title'),
      t('emergency.question'),
      [
        {
          text: t('emergency.call133'),
          onPress: () => {
            // En producción: Linking.openURL('tel:133')
            Alert.alert(t('emergency.calling'), t('emergency.connecting'));
          },
          style: 'destructive',
        },
        {
          text: t('emergency.evacuationRoutes'),
          onPress: () => {
            // Navegar a mapa de evacuación
            Alert.alert(t('emergency.navigating'), t('emergency.openingRoutes'));
          },
        },
        {
          text: t('common.cancel'),
          style: 'cancel',
        },
      ]
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },

    scrollContent: {
      padding: Spacing.md,
    },

    heroCard: {
      marginBottom: Spacing.lg,
    },

    heroActions: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'flex-end',
      marginTop: Spacing.md,
    },

    statusBadge: {
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      paddingHorizontal: Spacing.sm,
      paddingVertical: Spacing.xs,
      borderRadius: BorderRadius.md,
    },

    statusCard: {
      marginBottom: Spacing.lg,
    },

    section: {
      marginBottom: Spacing.lg,
    },

    sectionTitle: {
      marginBottom: Spacing.md,
      fontWeight: '700',
    },

    alertCard: {
      borderLeftWidth: 4,
      marginBottom: Spacing.sm,
    },

    alertHeader: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: Spacing.xs,
    },

    alertMessage: {
      lineHeight: 20,
    },

    debugCard: {
      marginBottom: Spacing.lg,
      backgroundColor: colors.backgroundSecondary,
    },

    footer: {
      padding: Spacing.lg,
      alignItems: 'center',
      marginBottom: Spacing.xl,
    },
  });

  const formatAlertTime = (date: Date) => {
    const now = new Date();
    const diffHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));

    if (diffHours < 1) return t('common.lessThanHour', { defaultValue: 'Hace menos de 1 hora' });
    if (diffHours < 24) return t('common.hoursAgo', { count: diffHours, defaultValue: `Hace ${diffHours} horas` });

    const diffDays = Math.floor(diffHours / 24);
    return t('common.daysAgo', { count: diffDays, defaultValue: `Hace ${diffDays} días` });
  };

  // Configurar acciones rápidas con gradientes
  const quickActions: ActionItem[] = [
    {
      id: 'emergency',
      title: t('home.emergency'),
      icon: <AlertIcon size={32} color="white" strokeWidth={3} />,
      onPress: handleEmergencyAction,
      gradientColors: colors.gradientEmergency,
      size: 'large',
    },
    {
      id: 'map',
      title: t('home.interactiveMap'),
      icon: <MapIcon size={24} color="white" strokeWidth={2.5} />,
      onPress: () => router.push('/explore'),
      gradientColors: colors.gradientOcean,
    },
    {
      id: 'precursors',
      title: t('home.precursors'),
      icon: <AlertIcon size={24} color="white" strokeWidth={2.5} />,
      onPress: () => router.push('/precursors'),
      gradientColors: colors.gradientVolcano,
    },
    {
      id: 'guides',
      title: t('home.safetyGuides'),
      icon: <InfoIcon size={24} color="white" strokeWidth={2.5} />,
      onPress: () => Alert.alert(t('emergency.navigating'), 'Abriendo guías de preparación...'),
      gradientColors: colors.gradientMountain,
    },
    {
      id: 'news',
      title: t('home.officialNews'),
      icon: <BellIcon size={24} color="white" strokeWidth={2.5} />,
      onPress: () => Alert.alert(t('emergency.navigating'), 'Abriendo información oficial...'),
      gradientColors: colors.gradientPrimary,
    },
  ];

  return (
    <View style={styles.container}>
      {/* Modern Header */}
      <ModernHeader
        title={t('home.title')}
        subtitle={t('home.subtitle')}
        leftIcon={<VolcanoIcon size={24} color="white" strokeWidth={2.5} />}
        rightIcon={<LanguageSelector compact showLabel={false} />}
        gradient={true}
        gradientColors={colors.gradientSunset}
      />

      {/* User Header */}
      <UserHeader showGreeting={true} />

      <ScrollView
        style={styles.scrollContent}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor={colors.tint}
            colors={[colors.tint]}
          />
        }
        showsVerticalScrollIndicator={false}
      >
        {/* Hero Card con imagen del volcán */}
        <HeroCard
          title={volcanoData.name}
          subtitle={`${volcanoData.location} • Estado: ${volcanoData.alertLevel}`}
          gradientColors={colors.gradientVolcano}
          height={220}
          style={styles.heroCard}
        >
          <View style={styles.heroActions}>
            <View style={styles.statusBadge}>
              <AccessibleText variant="caption" color="white">
                {t('home.lastUpdate', { time: formatAlertTime(volcanoData.lastUpdate) })}
              </AccessibleText>
            </View>
          </View>
        </HeroCard>

        {/* Estado del Volcán en Card Moderno */}
        <ModernCard elevation="medium" style={styles.statusCard}>
          <AccessibleText variant="h3" style={styles.sectionTitle}>
            {t('home.currentStatus')}
          </AccessibleText>
          <LiveVolcanoStatus />
        </ModernCard>

        {/* Acciones Rápidas con Grid Moderno */}
        <View style={styles.section}>
          <AccessibleText variant="h3" style={styles.sectionTitle}>
            {t('home.quickActions')}
          </AccessibleText>
          <ActionGrid actions={quickActions} />
        </View>

        {/* Alertas Recientes en Cards Modernas */}
        <View style={styles.section}>
          <AccessibleText variant="h3" style={styles.sectionTitle}>
            {t('home.recentUpdates')}
          </AccessibleText>
          {alerts.map((alert) => (
            <ModernCard
              key={alert.id}
              elevation="low"
              padding="md"
              style={[
                styles.alertCard,
                { borderLeftColor: alert.type === 'info' ? colors.info : colors.warning }
              ]}
            >
              <View style={styles.alertHeader}>
                <AccessibleText variant="button">{alert.title}</AccessibleText>
                <AccessibleText variant="caption" color="muted">
                  {formatAlertTime(alert.time)}
                </AccessibleText>
              </View>
              <AccessibleText variant="body" color="secondary" style={styles.alertMessage}>
                {alert.message}
              </AccessibleText>
            </ModernCard>
          ))}
        </View>

        {/* Connection Status - Solo en desarrollo */}
        {__DEV__ && (
          <ModernCard elevation="low" style={styles.debugCard}>
            <ConnectionStatus />
          </ModernCard>
        )}

        {/* Footer */}
        <View style={styles.footer}>
          <AccessibleText variant="caption" color="muted">
            {t('home.footer.data')}
          </AccessibleText>
          <AccessibleText variant="caption" color="muted">
            {t('home.footer.pullToRefresh')}
          </AccessibleText>
        </View>
      </ScrollView>
    </View>
  );
}
