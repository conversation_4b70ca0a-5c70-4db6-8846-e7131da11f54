/**
 * 🌋 Volcano App Mobile - Register Screen
 * Pantalla de registro con diseño moderno
 */

import React, { useState, useCallback, useMemo, useRef } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

import { useAuth } from '@/hooks/useAuth';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useI18n } from '@/hooks/useI18n';

import { AccessibleText } from '@/components/ui/AccessibleText';
import { PrimaryButton, SecondaryButton } from '@/components/ui/AccessibleButton';
import { ModernCard } from '@/components/ui/ModernCard';
import { ModernIcon } from '@/components/ui/ModernIcon';

import { Colors } from '@/constants/Colors';
import { BorderRadius, Layout, Spacing } from '@/constants/Layout';

export default function RegisterScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t } = useI18n();
  const { register, isLoading, error, clearError } = useAuth();

  // Estados del formulario
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    password: '',
    confirmPassword: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Referencias para los inputs
  const nameInputRef = useRef<TextInput>(null);
  const emailInputRef = useRef<TextInput>(null);
  const phoneInputRef = useRef<TextInput>(null);
  const passwordInputRef = useRef<TextInput>(null);
  const confirmPasswordInputRef = useRef<TextInput>(null);

  // Validación del formulario memoizada
  const isFormValid = useMemo(() =>
    formData.name.trim() !== '' &&
    formData.email.trim() !== '' &&
    formData.password.length >= 6 &&
    formData.password === formData.confirmPassword,
    [formData]
  );

  // Callback optimizado para actualizar form data
  const updateFormData = useCallback((field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  // Callbacks memoizados para evitar re-renders
  const handleRegister = useCallback(async () => {
    if (!isFormValid) {
      Alert.alert(
        t('auth.error.title'),
        t('auth.error.invalidForm')
      );
      return;
    }

    try {
      setIsSubmitting(true);
      clearError();

      const response = await register({
        name: formData.name.trim(),
        email: formData.email.trim().toLowerCase(),
        phone: formData.phone.trim() || undefined,
        password: formData.password,
      });

      if (response.success) {
        Alert.alert(
          t('auth.register.success.title'),
          t('auth.register.success.message'),
          [
            {
              text: t('common.ok'),
              onPress: () => router.replace('/(tabs)'),
            },
          ]
        );
      } else {
        Alert.alert(
          t('auth.error.title'),
          response.error || t('auth.error.registerFailed')
        );
      }
    } catch (error) {
      console.error('❌ Register error:', error);
      Alert.alert(
        t('auth.error.title'),
        t('auth.error.networkError')
      );
    } finally {
      setIsSubmitting(false);
    }
  }, [isFormValid, formData, register, clearError, t]);

  const handleLoginRedirect = useCallback(() => {
    router.push('/(auth)/login');
  }, []);

  // Callbacks optimizados para navegación entre inputs
  const handleNameSubmit = useCallback(() => {
    emailInputRef.current?.focus();
  }, []);

  const handleEmailSubmit = useCallback(() => {
    phoneInputRef.current?.focus();
  }, []);

  const handlePhoneSubmit = useCallback(() => {
    passwordInputRef.current?.focus();
  }, []);

  const handlePasswordSubmit = useCallback(() => {
    confirmPasswordInputRef.current?.focus();
  }, []);

  const handleConfirmPasswordSubmit = useCallback(() => {
    if (isFormValid) {
      handleRegister();
    }
  }, [isFormValid, handleRegister]);

  // Callbacks optimizados para toggle de contraseñas
  const handleTogglePassword = useCallback(() => {
    setShowPassword(prev => !prev);
  }, []);

  const handleToggleConfirmPassword = useCallback(() => {
    setShowConfirmPassword(prev => !prev);
  }, []);

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <LinearGradient
        colors={[colors.background, colors.tint + '20']}
        style={styles.gradient}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          keyboardDismissMode="none"
          scrollEnabled={true}
          nestedScrollEnabled={true}
        >
          {/* Header */}
          <View style={styles.header}>
            <ModernIcon
              name="volcano"
              size={64}
              color={colors.tint}
              style={styles.logo}
            />
            <AccessibleText
              variant="title"
              style={[styles.title, { color: colors.text }]}
            >
              {t('auth.register.title')}
            </AccessibleText>
            <AccessibleText
              variant="body"
              color="secondary"
              style={styles.subtitle}
            >
              {t('auth.register.subtitle')}
            </AccessibleText>
          </View>

          {/* Formulario */}
          <ModernCard style={styles.formCard}>
            {/* Name */}
            <View style={styles.inputGroup}>
              <AccessibleText
                variant="button"
                style={[styles.label, { color: colors.text }]}
              >
                {t('auth.fields.name')} *
              </AccessibleText>
              <View style={[styles.inputContainer, { borderColor: colors.border }]}>
                <ModernIcon
                  name="user"
                  size={20}
                  color={colors.icon}
                  style={styles.inputIcon}
                />
                <TextInput
                  ref={nameInputRef}
                  style={[styles.input, { color: colors.text }]}
                  value={formData.name}
                  onChangeText={(value) => updateFormData('name', value)}
                  placeholder={t('auth.placeholders.name')}
                  placeholderTextColor={colors.textSecondary}
                  autoCapitalize="words"
                  autoCorrect={false}
                  autoComplete="name"
                  returnKeyType="next"
                  onSubmitEditing={handleNameSubmit}
                  blurOnSubmit={false}
                  textContentType="name"
                />
              </View>
            </View>

            {/* Email */}
            <View style={styles.inputGroup}>
              <AccessibleText
                variant="button"
                style={[styles.label, { color: colors.text }]}
              >
                {t('auth.fields.email')} *
              </AccessibleText>
              <View style={[styles.inputContainer, { borderColor: colors.border }]}>
                <ModernIcon
                  name="mail"
                  size={20}
                  color={colors.icon}
                  style={styles.inputIcon}
                />
                <TextInput
                  ref={emailInputRef}
                  style={[styles.input, { color: colors.text }]}
                  value={formData.email}
                  onChangeText={(value) => updateFormData('email', value)}
                  placeholder={t('auth.placeholders.email')}
                  placeholderTextColor={colors.textSecondary}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  autoComplete="email"
                  returnKeyType="next"
                  onSubmitEditing={handleEmailSubmit}
                  blurOnSubmit={false}
                  textContentType="emailAddress"
                />
              </View>
            </View>

            {/* Phone */}
            <View style={styles.inputGroup}>
              <AccessibleText
                variant="button"
                style={[styles.label, { color: colors.text }]}
              >
                {t('auth.fields.phone')}
              </AccessibleText>
              <View style={[styles.inputContainer, { borderColor: colors.border }]}>
                <ModernIcon
                  name="phone"
                  size={20}
                  color={colors.icon}
                  style={styles.inputIcon}
                />
                <TextInput
                  ref={phoneInputRef}
                  style={[styles.input, { color: colors.text }]}
                  value={formData.phone}
                  onChangeText={(value) => updateFormData('phone', value)}
                  placeholder={t('auth.placeholders.phone')}
                  placeholderTextColor={colors.textSecondary}
                  keyboardType="phone-pad"
                  autoComplete="tel"
                  returnKeyType="next"
                  onSubmitEditing={handlePhoneSubmit}
                  blurOnSubmit={false}
                  textContentType="telephoneNumber"
                />
              </View>
            </View>

            {/* Password */}
            <View style={styles.inputGroup}>
              <AccessibleText
                variant="button"
                style={[styles.label, { color: colors.text }]}
              >
                {t('auth.fields.password')} *
              </AccessibleText>
              <View style={[styles.inputContainer, { borderColor: colors.border }]}>
                <ModernIcon
                  name="lock"
                  size={20}
                  color={colors.icon}
                  style={styles.inputIcon}
                />
                <TextInput
                  ref={passwordInputRef}
                  style={[styles.input, { color: colors.text }]}
                  value={formData.password}
                  onChangeText={(value) => updateFormData('password', value)}
                  placeholder={t('auth.placeholders.password')}
                  placeholderTextColor={colors.textSecondary}
                  secureTextEntry={!showPassword}
                  autoComplete="new-password"
                  returnKeyType="next"
                  onSubmitEditing={handlePasswordSubmit}
                  blurOnSubmit={false}
                  textContentType="newPassword"
                />
                <TouchableOpacity
                  onPress={handleTogglePassword}
                  style={styles.passwordToggle}
                >
                  <ModernIcon
                    name={showPassword ? 'eye-off' : 'eye'}
                    size={20}
                    color={colors.icon}
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Confirm Password */}
            <View style={styles.inputGroup}>
              <AccessibleText
                variant="button"
                style={[styles.label, { color: colors.text }]}
              >
                {t('auth.fields.confirmPassword')} *
              </AccessibleText>
              <View style={[styles.inputContainer, { borderColor: colors.border }]}>
                <ModernIcon
                  name="lock"
                  size={20}
                  color={colors.icon}
                  style={styles.inputIcon}
                />
                <TextInput
                  ref={confirmPasswordInputRef}
                  style={[styles.input, { color: colors.text }]}
                  value={formData.confirmPassword}
                  onChangeText={(value) => updateFormData('confirmPassword', value)}
                  placeholder={t('auth.placeholders.confirmPassword')}
                  placeholderTextColor={colors.textSecondary}
                  secureTextEntry={!showConfirmPassword}
                  autoComplete="new-password"
                  returnKeyType="done"
                  onSubmitEditing={handleConfirmPasswordSubmit}
                  blurOnSubmit={false}
                  textContentType="newPassword"
                />
                <TouchableOpacity
                  onPress={handleToggleConfirmPassword}
                  style={styles.passwordToggle}
                >
                  <ModernIcon
                    name={showConfirmPassword ? 'eye-off' : 'eye'}
                    size={20}
                    color={colors.icon}
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Password Requirements */}
            <View style={styles.passwordRequirements}>
              <AccessibleText
                variant="bodySmall"
                color="secondary"
                style={styles.requirementText}
              >
                {t('auth.register.passwordRequirements')}
              </AccessibleText>
            </View>

            {/* Register Button */}
            <PrimaryButton
              onPress={handleRegister}
              disabled={!isFormValid || isSubmitting || isLoading}
              style={styles.registerButton}
            >
              {isSubmitting || isLoading
                ? t('auth.register.creating')
                : t('auth.register.createAccount')
              }
            </PrimaryButton>

            {/* Error Message */}
            {error && (
              <View style={styles.errorContainer}>
                <ModernIcon
                  name="alert-circle"
                  size={16}
                  color={colors.error}
                  style={styles.errorIcon}
                />
                <AccessibleText
                  variant="bodySmall"
                  style={[styles.errorText, { color: colors.error }]}
                >
                  {error}
                </AccessibleText>
              </View>
            )}
          </ModernCard>

          {/* Login Link */}
          <View style={styles.loginContainer}>
            <AccessibleText
              variant="body"
              color="secondary"
              style={styles.loginText}
            >
              {t('auth.register.hasAccount')}
            </AccessibleText>
            <SecondaryButton
              onPress={handleLoginRedirect}
              style={styles.loginButton}
            >
              {t('auth.register.signIn')}
            </SecondaryButton>
          </View>
        </ScrollView>
      </LinearGradient>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: Spacing.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  logo: {
    marginBottom: Spacing.md,
  },
  title: {
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  subtitle: {
    textAlign: 'center',
    maxWidth: 280,
  },
  formCard: {
    marginBottom: Spacing.lg,
  },
  inputGroup: {
    marginBottom: Spacing.md,
  },
  label: {
    marginBottom: Spacing.xs,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    height: 48,
  },
  inputIcon: {
    marginRight: Spacing.sm,
  },
  input: {
    flex: 1,
    fontSize: 16,
    height: '100%',
  },
  passwordToggle: {
    padding: Spacing.xs,
  },
  passwordRequirements: {
    marginBottom: Spacing.lg,
  },
  requirementText: {
    textAlign: 'center',
  },
  registerButton: {
    marginBottom: Spacing.md,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.sm,
  },
  errorIcon: {
    marginRight: Spacing.xs,
  },
  errorText: {
    flex: 1,
  },
  loginContainer: {
    alignItems: 'center',
  },
  loginText: {
    marginBottom: Spacing.sm,
  },
  loginButton: {
    minWidth: 120,
  },
});
