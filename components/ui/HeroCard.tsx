/**
 * Hero Card Component
 * Modern card with background image, gradient overlay, and elevated content
 * Inspired by travel app designs
 */

import React from 'react';
import {
  View,
  StyleSheet,
  ImageBackground,
  ViewStyle,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { AccessibleText } from './AccessibleText';
import { Colors } from '@/constants/Colors';
import { BorderRadius, Spacing } from '@/constants/Layout';
import { useColorScheme } from '@/hooks/useColorScheme';

interface HeroCardProps {
  backgroundImage?: any;
  gradientColors?: string[];
  title: string;
  subtitle?: string;
  children?: React.ReactNode;
  style?: ViewStyle;
  height?: number;
}

export function HeroCard({
  backgroundImage,
  gradientColors = ['rgba(0,0,0,0.4)', 'rgba(0,0,0,0.6)'],
  title,
  subtitle,
  children,
  style,
  height = 200,
}: HeroCardProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const cardContent = (
    <>
      <LinearGradient
        colors={gradientColors}
        style={StyleSheet.absoluteFillObject}
        start={{ x: 0, y: 0 }}
        end={{ x: 0, y: 1 }}
      />
      <View style={styles.content}>
        <AccessibleText variant="h2" color="white" style={styles.title}>
          {title}
        </AccessibleText>
        {subtitle && (
          <AccessibleText variant="body" color="white" style={styles.subtitle}>
            {subtitle}
          </AccessibleText>
        )}
        {children}
      </View>
    </>
  );

  const cardStyle = [
    styles.container,
    { height, backgroundColor: colors.backgroundSecondary },
    style,
  ];

  if (backgroundImage) {
    return (
      <View style={cardStyle}>
        <ImageBackground
          source={backgroundImage}
          style={styles.backgroundImage}
          imageStyle={styles.backgroundImageStyle}
        >
          {cardContent}
        </ImageBackground>
      </View>
    );
  }

  return (
    <View style={cardStyle}>
      {cardContent}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    borderRadius: BorderRadius.xl,
    overflow: 'hidden',
    marginBottom: Spacing.md,
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.1,
        shadowRadius: 12,
      },
      android: {
        elevation: 6,
      },
    }),
  },

  backgroundImage: {
    flex: 1,
    justifyContent: 'flex-end',
  },

  backgroundImageStyle: {
    borderRadius: BorderRadius.xl,
  },

  content: {
    padding: Spacing.lg,
    justifyContent: 'flex-end',
    flex: 1,
  },

  title: {
    fontWeight: '700',
    marginBottom: Spacing.xs,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },

  subtitle: {
    opacity: 0.9,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
});