/**
 * 🌋 Volcano App - Análisis Matemático de Precursores Volcánicos
 * Módulo para calcular derivadas y determinar niveles de alerta dinámicos
 */

import {
  AnalisisPrecursor,
  ResultadoAlerta,
  NivelAlerta,
  UmbralesAlerta,
  UMBRALES_DEFAULT,
  MENSAJES_ALERTA,
  OpcionesAnalisis,
} from '@/types/precursor';

/**
 * Valida que los datos de entrada sean válidos para el análisis
 * @param datos Array de números a validar
 * @returns true si los datos son válidos
 */
export function validarDatos(datos: number[]): boolean {
  if (!Array.isArray(datos)) {
    return false;
  }
  
  if (datos.length < 2) {
    return false;
  }
  
  // Verificar que todos los elementos sean números válidos
  return datos.every(valor => 
    typeof valor === 'number' && 
    !isNaN(valor) && 
    isFinite(valor)
  );
}

/**
 * Filtra valores inválidos del array de datos
 * @param datos Array de números que puede contener valores inválidos
 * @returns Array filtrado con solo valores válidos
 */
export function filtrarValoresInvalidos(datos: number[]): number[] {
  return datos.filter(valor => 
    typeof valor === 'number' && 
    !isNaN(valor) && 
    isFinite(valor)
  );
}

/**
 * Calcula la primera derivada (tasa de cambio) de un array de datos
 * @param datos Array de números
 * @returns Array con la primera derivada, primer elemento es null
 */
export function calcularPrimeraDerivada(datos: number[]): (number | null)[] {
  if (!validarDatos(datos)) {
    throw new Error('Datos inválidos para calcular primera derivada');
  }
  
  const derivada: (number | null)[] = [null]; // Primer elemento es null
  
  for (let i = 1; i < datos.length; i++) {
    derivada.push(datos[i] - datos[i - 1]);
  }
  
  return derivada;
}

/**
 * Calcula la segunda derivada (aceleración) de la primera derivada
 * @param primeraderivada Array con la primera derivada
 * @returns Array con la segunda derivada, primeros dos elementos son null
 */
export function calcularSegundaDerivada(primeraderivada: (number | null)[]): (number | null)[] {
  if (primeraderivada.length < 2) {
    throw new Error('Primera derivada insuficiente para calcular segunda derivada');
  }
  
  const segundaDerivada: (number | null)[] = [null, null]; // Primeros dos elementos son null
  
  for (let i = 2; i < primeraderivada.length; i++) {
    const valorActual = primeraderivada[i];
    const valorAnterior = primeraderivada[i - 1];
    
    if (valorActual !== null && valorAnterior !== null) {
      segundaDerivada.push(valorActual - valorAnterior);
    } else {
      segundaDerivada.push(null);
    }
  }
  
  return segundaDerivada;
}

/**
 * Función principal que analiza los datos de precursores
 * @param datosPrecursor Array de números con los datos originales
 * @param opciones Opciones adicionales para el análisis
 * @returns Objeto con datos originales, primera derivada y segunda derivada
 */
export function analizarPrecursor(
  datosPrecursor: number[], 
  opciones: OpcionesAnalisis = {}
): AnalisisPrecursor {
  // Validar y filtrar datos si es necesario
  let datosLimpios = datosPrecursor;
  
  if (opciones.validarDatos !== false) {
    if (!validarDatos(datosPrecursor)) {
      throw new Error('Los datos de precursores no son válidos');
    }
  }
  
  if (opciones.filtrarInvalidos) {
    datosLimpios = filtrarValoresInvalidos(datosPrecursor);
    if (datosLimpios.length < 2) {
      throw new Error('Datos insuficientes después del filtrado');
    }
  }
  
  // Calcular derivadas
  const primeraderivada = calcularPrimeraDerivada(datosLimpios);
  const segundaDerivada = calcularSegundaDerivada(primeraderivada);
  
  return {
    datosOriginales: datosLimpios,
    primeraderivada,
    segundaDerivada,
    timestamps: opciones.incluirTimestamps ? 
      datosLimpios.map((_, i) => new Date(Date.now() + i * 60000)) : // Timestamps cada minuto
      undefined,
  };
}

/**
 * Obtiene el último valor válido de un array que puede contener nulls
 * @param array Array que puede contener valores null
 * @returns Último valor válido o null si no hay ninguno
 */
export function obtenerUltimoValorValido(array: (number | null)[]): number | null {
  for (let i = array.length - 1; i >= 0; i--) {
    if (array[i] !== null) {
      return array[i];
    }
  }
  return null;
}

/**
 * Determina el nivel de alerta basado en la segunda derivada
 * @param segundaDerivada Array con la segunda derivada
 * @param umbrales Umbrales personalizados (opcional)
 * @returns Resultado con nivel de alerta, mensaje y valor
 */
export function determinarNivelAlerta(
  segundaDerivada: (number | null)[],
  umbrales: UmbralesAlerta = UMBRALES_DEFAULT
): ResultadoAlerta {
  const ultimoValor = obtenerUltimoValorValido(segundaDerivada);
  
  if (ultimoValor === null) {
    return {
      nivel: 'Verde',
      mensaje: 'No hay datos suficientes para determinar el nivel',
      valor: null,
      timestamp: new Date(),
    };
  }
  
  let nivel: NivelAlerta;
  let mensaje: string;
  
  if (ultimoValor <= umbrales.verde) {
    nivel = 'Verde';
    mensaje = MENSAJES_ALERTA.Verde;
  } else if (ultimoValor <= umbrales.amarillo) {
    nivel = 'Amarillo';
    mensaje = MENSAJES_ALERTA.Amarillo;
  } else {
    nivel = 'Rojo';
    mensaje = MENSAJES_ALERTA.Rojo;
  }
  
  // Encontrar el índice del último valor válido
  let indice: number | undefined;
  for (let i = segundaDerivada.length - 1; i >= 0; i--) {
    if (segundaDerivada[i] !== null) {
      indice = i;
      break;
    }
  }
  
  return {
    nivel,
    mensaje,
    valor: ultimoValor,
    timestamp: new Date(),
    indice,
  };
}

/**
 * Análisis completo de precursores con determinación de alerta
 * @param datosPrecursor Array de datos originales
 * @param umbrales Umbrales personalizados (opcional)
 * @param opciones Opciones adicionales para el análisis
 * @returns Análisis completo y resultado de alerta
 */
export function analisisCompleto(
  datosPrecursor: number[],
  umbrales: UmbralesAlerta = UMBRALES_DEFAULT,
  opciones: OpcionesAnalisis = {}
): { analisis: AnalisisPrecursor; alerta: ResultadoAlerta } {
  const analisis = analizarPrecursor(datosPrecursor, opciones);
  const alerta = determinarNivelAlerta(analisis.segundaDerivada, umbrales);
  
  return { analisis, alerta };
}

/**
 * Función de utilidad para formatear valores numéricos
 * @param valor Valor a formatear
 * @param decimales Número de decimales
 * @returns Valor formateado como string
 */
export function formatearValor(valor: number | null, decimales: number = 2): string {
  if (valor === null) {
    return 'N/A';
  }
  return valor.toFixed(decimales);
}

/**
 * Función de utilidad para obtener estadísticas básicas de un array
 * @param datos Array de números (puede contener nulls)
 * @returns Estadísticas básicas
 */
export function obtenerEstadisticas(datos: (number | null)[]): {
  min: number | null;
  max: number | null;
  promedio: number | null;
  count: number;
} {
  const valoresValidos = datos.filter((v): v is number => v !== null);
  
  if (valoresValidos.length === 0) {
    return { min: null, max: null, promedio: null, count: 0 };
  }
  
  const min = Math.min(...valoresValidos);
  const max = Math.max(...valoresValidos);
  const promedio = valoresValidos.reduce((sum, val) => sum + val, 0) / valoresValidos.length;
  
  return { min, max, promedio, count: valoresValidos.length };
}
