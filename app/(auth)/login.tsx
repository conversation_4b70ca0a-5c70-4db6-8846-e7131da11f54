/**
 * 🌋 Volcano App Mobile - Login Screen
 * Pantalla de inicio de sesión con diseño moderno
 */

import React, { useState, useCallback, useMemo, useRef } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Alert,
  TextInput,
  TouchableOpacity,
} from 'react-native';
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

import { useAuth } from '@/hooks/useAuth';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useI18n } from '@/hooks/useI18n';

import { AccessibleText } from '@/components/ui/AccessibleText';
import { PrimaryButton, SecondaryButton } from '@/components/ui/AccessibleButton';
import { ModernCard } from '@/components/ui/ModernCard';
import { ModernIcon } from '@/components/ui/ModernIcon';

import { Colors } from '@/constants/Colors';
import { BorderRadius, Layout, Spacing } from '@/constants/Layout';

export default function LoginScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t } = useI18n();

  // Optimizar el hook de auth para evitar re-renders innecesarios
  const { login, isLoading, error, clearError } = useAuth();

  // Estados del formulario
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Referencias para los inputs
  const emailInputRef = useRef<TextInput>(null);
  const passwordInputRef = useRef<TextInput>(null);

  // Validación del formulario memoizada
  const isFormValid = useMemo(() =>
    email.trim() !== '' && password.length >= 6,
    [email, password]
  );

  // Callbacks memoizados para evitar re-renders
  const handleLogin = useCallback(async () => {
    if (!isFormValid) {
      Alert.alert(
        t('auth.error.title'),
        t('auth.error.invalidForm')
      );
      return;
    }

    try {
      setIsSubmitting(true);
      clearError();

      const response = await login({
        email: email.trim().toLowerCase(),
        password: password,
      });

      if (response.success) {
        // Redireccionar a la pantalla principal
        router.replace('/(tabs)');
      } else {
        Alert.alert(
          t('auth.error.title'),
          response.error || t('auth.error.loginFailed')
        );
      }
    } catch (error) {
      console.error('❌ Login error:', error);
      Alert.alert(
        t('auth.error.title'),
        t('auth.error.networkError')
      );
    } finally {
      setIsSubmitting(false);
    }
  }, [isFormValid, email, password, login, clearError, t]);

  const handleRegisterRedirect = useCallback(() => {
    router.push('/(auth)/register');
  }, []);

  const handleForgotPassword = useCallback(() => {
    // TODO: Implementar recuperación de contraseña
    Alert.alert(
      t('auth.forgotPassword.title'),
      t('auth.forgotPassword.comingSoon')
    );
  }, [t]);

  // Callbacks optimizados para los inputs
  const handleEmailChange = useCallback((text: string) => {
    setEmail(text);
  }, []);

  const handlePasswordChange = useCallback((text: string) => {
    setPassword(text);
  }, []);

  const handleTogglePassword = useCallback(() => {
    setShowPassword(prev => !prev);
  }, []);

  const handleEmailSubmit = useCallback(() => {
    passwordInputRef.current?.focus();
  }, []);

  const handlePasswordSubmit = useCallback(() => {
    if (isFormValid) {
      handleLogin();
    }
  }, [isFormValid, handleLogin]);

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 20}
    >
      <LinearGradient
        colors={[colors.background, colors.tint + '20']}
        style={styles.gradient}
      >
        <ScrollView
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
          keyboardShouldPersistTaps="handled"
          keyboardDismissMode="none"
          scrollEnabled={true}
          nestedScrollEnabled={true}
        >
          {/* Header */}
          <View style={styles.header}>
            <ModernIcon
              name="volcano"
              size={64}
              color={colors.tint}
              style={styles.logo}
            />
            <AccessibleText
              variant="title"
              style={[styles.title, { color: colors.text }]}
            >
              {t('auth.login.title')}
            </AccessibleText>
            <AccessibleText
              variant="body"
              color="secondary"
              style={styles.subtitle}
            >
              {t('auth.login.subtitle')}
            </AccessibleText>
          </View>

          {/* Formulario */}
          <ModernCard style={styles.formCard}>
            {/* Email */}
            <View style={styles.inputGroup}>
              <AccessibleText
                variant="button"
                style={[styles.label, { color: colors.text }]}
              >
                {t('auth.fields.email')}
              </AccessibleText>
              <View style={[styles.inputContainer, { borderColor: colors.border }]}>
                <ModernIcon
                  name="mail"
                  size={20}
                  color={colors.icon}
                  style={styles.inputIcon}
                />
                <TextInput
                  ref={emailInputRef}
                  style={[styles.input, { color: colors.text }]}
                  value={email}
                  onChangeText={handleEmailChange}
                  placeholder={t('auth.placeholders.email')}
                  placeholderTextColor={colors.textSecondary}
                  keyboardType="email-address"
                  autoCapitalize="none"
                  autoCorrect={false}
                  autoComplete="email"
                  returnKeyType="next"
                  onSubmitEditing={handleEmailSubmit}
                  blurOnSubmit={false}
                  textContentType="emailAddress"
                />
              </View>
            </View>

            {/* Password */}
            <View style={styles.inputGroup}>
              <AccessibleText
                variant="button"
                style={[styles.label, { color: colors.text }]}
              >
                {t('auth.fields.password')}
              </AccessibleText>
              <View style={[styles.inputContainer, { borderColor: colors.border }]}>
                <ModernIcon
                  name="lock"
                  size={20}
                  color={colors.icon}
                  style={styles.inputIcon}
                />
                <TextInput
                  ref={passwordInputRef}
                  style={[styles.input, { color: colors.text }]}
                  value={password}
                  onChangeText={handlePasswordChange}
                  placeholder={t('auth.placeholders.password')}
                  placeholderTextColor={colors.textSecondary}
                  secureTextEntry={!showPassword}
                  autoComplete="password"
                  returnKeyType="done"
                  onSubmitEditing={handlePasswordSubmit}
                  blurOnSubmit={false}
                  textContentType="password"
                />
                <TouchableOpacity
                  onPress={handleTogglePassword}
                  style={styles.passwordToggle}
                >
                  <ModernIcon
                    name={showPassword ? 'eye-off' : 'eye'}
                    size={20}
                    color={colors.icon}
                  />
                </TouchableOpacity>
              </View>
            </View>

            {/* Forgot Password */}
            <TouchableOpacity
              onPress={handleForgotPassword}
              style={styles.forgotPassword}
            >
              <AccessibleText
                variant="bodySmall"
                style={[styles.forgotPasswordText, { color: colors.tint }]}
              >
                {t('auth.login.forgotPassword')}
              </AccessibleText>
            </TouchableOpacity>

            {/* Login Button */}
            <PrimaryButton
              onPress={handleLogin}
              disabled={!isFormValid || isSubmitting || isLoading}
              style={styles.loginButton}
            >
              {isSubmitting || isLoading
                ? t('auth.login.signingIn')
                : t('auth.login.signIn')
              }
            </PrimaryButton>

            {/* Error Message */}
            {error && (
              <View style={styles.errorContainer}>
                <ModernIcon
                  name="alert-circle"
                  size={16}
                  color={colors.error}
                  style={styles.errorIcon}
                />
                <AccessibleText
                  variant="bodySmall"
                  style={[styles.errorText, { color: colors.error }]}
                >
                  {error}
                </AccessibleText>
              </View>
            )}
          </ModernCard>

          {/* Register Link */}
          <View style={styles.registerContainer}>
            <AccessibleText
              variant="body"
              color="secondary"
              style={styles.registerText}
            >
              {t('auth.login.noAccount')}
            </AccessibleText>
            <SecondaryButton
              onPress={handleRegisterRedirect}
              style={styles.registerButton}
            >
              {t('auth.login.signUp')}
            </SecondaryButton>
          </View>
        </ScrollView>
      </LinearGradient>
    </KeyboardAvoidingView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  gradient: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    justifyContent: 'center',
    padding: Spacing.lg,
  },
  header: {
    alignItems: 'center',
    marginBottom: Spacing.xl,
  },
  logo: {
    marginBottom: Spacing.md,
  },
  title: {
    marginBottom: Spacing.sm,
    textAlign: 'center',
  },
  subtitle: {
    textAlign: 'center',
    maxWidth: 280,
  },
  formCard: {
    marginBottom: Spacing.lg,
  },
  inputGroup: {
    marginBottom: Spacing.md,
  },
  label: {
    marginBottom: Spacing.xs,
  },
  inputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.md,
    height: 48,
  },
  inputIcon: {
    marginRight: Spacing.sm,
  },
  input: {
    flex: 1,
    fontSize: 16,
    height: '100%',
  },
  passwordToggle: {
    padding: Spacing.xs,
  },
  forgotPassword: {
    alignSelf: 'flex-end',
    marginBottom: Spacing.lg,
  },
  forgotPasswordText: {
    textDecorationLine: 'underline',
  },
  loginButton: {
    marginBottom: Spacing.md,
  },
  errorContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: Spacing.sm,
  },
  errorIcon: {
    marginRight: Spacing.xs,
  },
  errorText: {
    flex: 1,
  },
  registerContainer: {
    alignItems: 'center',
  },
  registerText: {
    marginBottom: Spacing.sm,
  },
  registerButton: {
    minWidth: 120,
  },
});
