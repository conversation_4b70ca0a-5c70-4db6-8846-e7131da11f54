/**
 * 🌋 Volcano App - API Hooks
 * React Query hooks para gestión de datos de la API
 */

import { apiService, LocationReport } from '@/services/api';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';

// Query keys para React Query
export const queryKeys = {
  health: ['health'],
  config: ['mobile', 'config'],
  currentAlert: ['mobile', 'alerts', 'current'],
  zones: ['mobile', 'zones'],
  locationCheck: (lat: number, lng: number) => ['mobile', 'location', 'check', lat, lng],
  appVersion: (version: string) => ['mobile', 'version', version],
} as const;

// Hook para health check
export function useHealthCheck() {
  return useQuery({
    queryKey: queryKeys.health,
    queryFn: apiService.healthCheck,
    staleTime: 30000, // 30 segundos
    retry: 3,
  });
}

// Hook para configuración móvil
export function useMobileConfig() {
  return useQuery({
    queryKey: queryKeys.config,
    queryFn: apiService.getMobileConfig,
    staleTime: 5 * 60 * 1000, // 5 minutos
    retry: 2,
  });
}

// Hook para alerta actual
export function useCurrentAlert() {
  return useQuery({
    queryKey: queryKeys.currentAlert,
    queryFn: apiService.getCurrentAlert,
    staleTime: 30000, // 30 segundos
    refetchInterval: 60000, // Refetch cada minuto
    retry: 2,
  });
}

// Hook para zonas de seguridad
export function useSafetyZones() {
  return useQuery({
    queryKey: queryKeys.zones,
    queryFn: apiService.getAllZones,
    staleTime: 5 * 60 * 1000, // 5 minutos
    retry: 2,
  });
}

// Hook para verificar ubicación
export function useLocationCheck(lat?: number, lng?: number) {
  return useQuery({
    queryKey: lat && lng ? queryKeys.locationCheck(lat, lng) : [],
    queryFn: () => lat && lng ? apiService.checkLocation(lat, lng) : Promise.resolve(null),
    enabled: !!(lat && lng),
    staleTime: 60000, // 1 minuto
    retry: 1,
  });
}

// Hook para verificar versión de la app
export function useAppVersionCheck(currentVersion: string) {
  return useQuery({
    queryKey: queryKeys.appVersion(currentVersion),
    queryFn: () => apiService.checkAppVersion(currentVersion),
    staleTime: 24 * 60 * 60 * 1000, // 24 horas
    retry: 1,
  });
}

// Hook para reportar ubicación
export function useLocationReport() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (locationData: Omit<LocationReport, 'anonymous_id'>) => {
      // Importar dinámicamente para evitar problemas con el IDE
      const { getDeviceId, validateDeviceId } = await import('@/services/deviceId');
      const Constants = await import('expo-constants');
      const { Platform } = await import('react-native');

      // Obtener device_id
      const deviceId = await getDeviceId();

      // Validar device_id
      if (!validateDeviceId(deviceId)) {
        throw new Error('Invalid device ID generated');
      }

      // Validar coordenadas
      if (locationData.latitude < -90 || locationData.latitude > 90) {
        throw new Error('Invalid latitude. Must be between -90 and 90');
      }

      if (locationData.longitude < -180 || locationData.longitude > 180) {
        throw new Error('Invalid longitude. Must be between -180 and 180');
      }

      // Preparar datos completos para el reporte
      const completeLocationData: LocationReport = {
        anonymous_id: deviceId,
        latitude: locationData.latitude,
        longitude: locationData.longitude,
        accuracy: locationData.accuracy,
        timestamp: locationData.timestamp || new Date().toISOString(),
        app_version: locationData.app_version || Constants.default.expoConfig?.version || '1.0.0',
        device_type: locationData.device_type || Platform.OS,
      };

      return apiService.reportLocation(completeLocationData);
    },
    onSuccess: () => {
      // Invalidar cache de verificación de ubicación si existe
      queryClient.invalidateQueries({ queryKey: ['mobile', 'location', 'check'] });
    },
    onError: (error) => {
      console.error('Error reporting location:', error);
    },
  });
}

// Hook para sincronización masiva
export function useBulkSync() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (lastSync?: string) => apiService.bulkSync(lastSync),
    onSuccess: (data) => {
      // Actualizar cache con los datos sincronizados
      if (data.alerts.length > 0) {
        queryClient.setQueryData(queryKeys.currentAlert, data.alerts[0]);
      }
      
      if (data.zones.length > 0) {
        queryClient.setQueryData(queryKeys.zones, data.zones);
      }
      
      queryClient.setQueryData(queryKeys.config, data.config);
      
      console.log('✅ Bulk sync completed:', data.timestamp);
    },
    onError: (error) => {
      console.error('❌ Bulk sync failed:', error);
    },
  });
}

// Hook para reportar ubicaciones en lote
export function useBatchLocationReport() {
  return useMutation({
    mutationFn: async (locationsData: Omit<LocationReport, 'anonymous_id'>[]) => {
      // Importar dinámicamente para evitar problemas con el IDE
      const { getDeviceId, validateDeviceId } = await import('@/services/deviceId');
      const Constants = await import('expo-constants');
      const { Platform } = await import('react-native');

      // Obtener device_id
      const deviceId = await getDeviceId();

      // Validar device_id
      if (!validateDeviceId(deviceId)) {
        throw new Error('Invalid device ID generated');
      }

      // Procesar cada ubicación
      const completeLocations: LocationReport[] = locationsData.map(locationData => {
        // Validar coordenadas
        if (locationData.latitude < -90 || locationData.latitude > 90) {
          throw new Error(`Invalid latitude: ${locationData.latitude}. Must be between -90 and 90`);
        }

        if (locationData.longitude < -180 || locationData.longitude > 180) {
          throw new Error(`Invalid longitude: ${locationData.longitude}. Must be between -180 and 180`);
        }

        return {
          anonymous_id: deviceId,
          latitude: locationData.latitude,
          longitude: locationData.longitude,
          accuracy: locationData.accuracy,
          timestamp: locationData.timestamp || new Date().toISOString(),
          app_version: locationData.app_version || Constants.default.expoConfig?.version || '1.0.0',
          device_type: locationData.device_type || Platform.OS,
        };
      });

      return apiService.batchLocationReport(completeLocations);
    },
    onError: (error) => {
      console.error('Error in batch location report:', error);
    },
  });
}

// Hook personalizado para gestión completa de datos
export function useVolcanoData() {
  const healthQuery = useHealthCheck();
  const configQuery = useMobileConfig();
  const alertQuery = useCurrentAlert();
  const zonesQuery = useSafetyZones();

  const isLoading = healthQuery.isLoading || configQuery.isLoading || 
                   alertQuery.isLoading || zonesQuery.isLoading;

  const hasError = healthQuery.isError || configQuery.isError || 
                  alertQuery.isError || zonesQuery.isError;

  const isOnline = !healthQuery.isError;

  return {
    // Estados
    isLoading,
    hasError,
    isOnline,
    
    // Datos
    health: healthQuery.data,
    config: configQuery.data,
    currentAlert: alertQuery.data,
    zones: zonesQuery.data,
    
    // Funciones de refetch
    refetchAll: () => {
      healthQuery.refetch();
      configQuery.refetch();
      alertQuery.refetch();
      zonesQuery.refetch();
    },
    
    // Queries individuales para control granular
    queries: {
      health: healthQuery,
      config: configQuery,
      alert: alertQuery,
      zones: zonesQuery,
    },
  };
}

// Hook para gestión de ubicación con reportes automáticos
export function useLocationManager() {
  const locationReport = useLocationReport();
  const batchReport = useBatchLocationReport();

  const reportLocation = async (location: Omit<LocationReport, 'anonymous_id'>) => {
    try {
      await locationReport.mutateAsync(location);
    } catch (error) {
      // Si falla, guardar para reporte en lote más tarde
      console.warn('Location report failed, will retry in batch');
      // Aquí podrías guardar en AsyncStorage para retry offline
    }
  };

  const reportBatchLocations = async (locations: Omit<LocationReport, 'anonymous_id'>[]) => {
    try {
      await batchReport.mutateAsync(locations);
    } catch (error) {
      console.error('Batch location report failed:', error);
    }
  };

  return {
    reportLocation,
    reportBatchLocations,
    isReporting: locationReport.isPending || batchReport.isPending,
    error: locationReport.error || batchReport.error,
  };
}
