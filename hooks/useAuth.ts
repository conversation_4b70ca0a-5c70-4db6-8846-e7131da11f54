/**
 * 🌋 Volcano App Mobile - Authentication Hook
 * Hook personalizado para manejo de autenticación
 */

import { useAuth as useAuthContext } from '@/providers/AuthProvider';
import { useEffect, useState } from 'react';
import { getCurrentUserId, getAuthToken, isUserAuthenticated } from '@/services/auth';

// =====================================================
// HOOK PRINCIPAL DE AUTENTICACIÓN
// =====================================================

export function useAuth() {
  return useAuthContext();
}

// =====================================================
// HOOKS ESPECÍFICOS
// =====================================================

/**
 * Hook para obtener el estado de autenticación de forma simple
 */
export function useAuthState() {
  const { user, isAuthenticated, isLoading, isInitializing } = useAuth();
  
  return {
    user,
    isAuthenticated,
    isLoading: isLoading || isInitializing,
    userId: user?.id || null,
    userEmail: user?.email || null,
    userName: user?.name || null,
  };
}

/**
 * Hook para verificar si el usuario está autenticado
 */
export function useIsAuthenticated(): boolean {
  const { isAuthenticated, isInitializing } = useAuth();
  
  // Solo devolver true si está autenticado y ya terminó la inicialización
  return isAuthenticated && !isInitializing;
}

/**
 * Hook para obtener el token de autenticación
 */
export function useAuthToken() {
  const { session } = useAuth();
  const [token, setToken] = useState<string | null>(null);
  
  useEffect(() => {
    const updateToken = async () => {
      const authToken = await getAuthToken();
      setToken(authToken);
    };
    
    updateToken();
  }, [session]);
  
  return token;
}

/**
 * Hook para obtener el ID del usuario actual
 */
export function useCurrentUserId(): string | null {
  const { user } = useAuth();
  return user?.id || null;
}

/**
 * Hook para verificar si el usuario tiene un rol específico
 */
export function useUserRole() {
  const { user } = useAuth();
  
  // Por ahora todos los usuarios tienen rol 'user'
  // En el futuro se puede extender para roles como 'admin', 'family_leader', etc.
  return {
    role: user ? 'user' : null,
    isUser: !!user,
    isAdmin: false, // Para futuras implementaciones
    isFamilyLeader: false, // Para futuras implementaciones
  };
}

/**
 * Hook para manejar el estado de carga durante operaciones de auth
 */
export function useAuthLoading() {
  const { isLoading, isInitializing } = useAuth();
  
  return {
    isLoading: isLoading || isInitializing,
    isInitializing,
    isOperationLoading: isLoading && !isInitializing,
  };
}

/**
 * Hook para manejar errores de autenticación
 */
export function useAuthError() {
  const { error, clearError } = useAuth();
  
  return {
    error,
    hasError: !!error,
    clearError,
  };
}

/**
 * Hook para operaciones de autenticación (login, register, logout)
 */
export function useAuthOperations() {
  const { login, register, logout, refreshUser, updateProfile } = useAuth();
  
  return {
    login,
    register,
    logout,
    refreshUser,
    updateProfile,
  };
}

/**
 * Hook para verificar si se requiere autenticación
 */
export function useRequireAuth() {
  const { isAuthenticated, isInitializing } = useAuth();
  
  return {
    isAuthenticated,
    isInitializing,
    requiresAuth: !isAuthenticated && !isInitializing,
  };
}

/**
 * Hook para obtener información del perfil del usuario
 */
export function useUserProfile() {
  const { user, updateProfile, refreshUser } = useAuth();
  
  return {
    profile: user,
    updateProfile,
    refreshProfile: refreshUser,
    hasProfile: !!user,
    profileData: {
      id: user?.id,
      email: user?.email,
      name: user?.name,
      phone: user?.phone,
      avatarUrl: user?.avatar_url,
      createdAt: user?.created_at,
      updatedAt: user?.updated_at,
    },
  };
}

// =====================================================
// HOOKS DE UTILIDAD
// =====================================================

/**
 * Hook para verificar permisos específicos
 */
export function usePermissions() {
  const { user } = useAuth();
  
  return {
    canCreateFamilyPlan: !!user,
    canJoinFamilyPlan: !!user,
    canEditProfile: !!user,
    canAccessSettings: !!user,
    canReceiveNotifications: !!user,
  };
}

/**
 * Hook para obtener headers de autenticación para APIs
 */
export function useAuthHeaders() {
  const token = useAuthToken();
  const userId = useCurrentUserId();
  
  return {
    getHeaders: () => ({
      'Authorization': token ? `Bearer ${token}` : undefined,
      'X-User-ID': userId || undefined,
    }),
    hasAuthHeaders: !!token,
  };
}

/**
 * Hook para manejar redirecciones después de autenticación
 */
export function useAuthRedirect() {
  const { isAuthenticated, isInitializing } = useAuth();
  
  return {
    shouldRedirectToLogin: !isAuthenticated && !isInitializing,
    shouldRedirectToHome: isAuthenticated && !isInitializing,
    isReady: !isInitializing,
  };
}
