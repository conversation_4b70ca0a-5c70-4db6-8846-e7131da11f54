/**
 * 🌋 Volcano App Mobile - Family Plan Hooks
 * Hooks para manejo de planes familiares con React Query
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  familyPlanService, 
  FamilyPlan, 
  CreateFamilyPlanData, 
  JoinFamilyPlanData,
  EmergencyContact,
  EmergencyKit,
  MeetingPoint,
  EvacuationPlan,
  hasUserFamilyPlans,
  getUserPrimaryFamilyPlan
} from '@/services/familyPlan';
import { useAuth } from './useAuth';

// =====================================================
// QUERY KEYS
// =====================================================

export const familyPlanQueryKeys = {
  all: ['familyPlans'] as const,
  user: () => [...familyPlanQueryKeys.all, 'user'] as const,
  plan: (id: string) => [...familyPlanQueryKeys.all, 'plan', id] as const,
  planWithIncludes: (id: string, includes: string[]) => 
    [...familyPlanQueryKeys.plan(id), 'includes', includes] as const,
  evacuationPlan: (planId: string, alertLevel: string) => 
    [...familyPlanQueryKeys.plan(planId), 'evacuation', alertLevel] as const,
} as const;

// =====================================================
// HOOKS DE CONSULTA
// =====================================================

/**
 * Hook para obtener planes familiares del usuario
 */
export function useUserFamilyPlans() {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: familyPlanQueryKeys.user(),
    queryFn: async () => {
      const result = await familyPlanService.getUserFamilyPlans();
      if (!result.success) {
        throw new Error(result.error || 'Error al obtener planes familiares');
      }
      return result.data || [];
    },
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutos
    retry: 2,
  });
}

/**
 * Hook para obtener un plan familiar específico
 */
export function useFamilyPlan(planId: string | null, includes?: string[]) {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: includes 
      ? familyPlanQueryKeys.planWithIncludes(planId!, includes)
      : familyPlanQueryKeys.plan(planId!),
    queryFn: async () => {
      if (!planId) throw new Error('Plan ID is required');
      
      const result = await familyPlanService.getFamilyPlan(planId, includes);
      if (!result.success) {
        throw new Error(result.error || 'Error al obtener plan familiar');
      }
      return result.data!;
    },
    enabled: isAuthenticated && !!planId,
    staleTime: 2 * 60 * 1000, // 2 minutos
    retry: 2,
  });
}

/**
 * Hook para obtener el plan familiar principal del usuario
 */
export function usePrimaryFamilyPlan() {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: [...familyPlanQueryKeys.user(), 'primary'],
    queryFn: async () => {
      const plan = await getUserPrimaryFamilyPlan();
      return plan;
    },
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutos
    retry: 2,
  });
}

/**
 * Hook para verificar si el usuario tiene planes familiares
 */
export function useHasFamilyPlans() {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: [...familyPlanQueryKeys.user(), 'hasPlans'],
    queryFn: hasUserFamilyPlans,
    enabled: isAuthenticated,
    staleTime: 5 * 60 * 1000, // 5 minutos
    retry: 2,
  });
}

/**
 * Hook para obtener plan de evacuación actual
 */
export function useCurrentEvacuationPlan(planId: string | null, alertLevel: string) {
  const { isAuthenticated } = useAuth();

  return useQuery({
    queryKey: familyPlanQueryKeys.evacuationPlan(planId!, alertLevel),
    queryFn: async () => {
      if (!planId) throw new Error('Plan ID is required');
      
      const result = await familyPlanService.getCurrentEvacuationPlan(planId, alertLevel);
      if (!result.success) {
        throw new Error(result.error || 'Error al obtener plan de evacuación');
      }
      return result.data!;
    },
    enabled: isAuthenticated && !!planId && !!alertLevel,
    staleTime: 1 * 60 * 1000, // 1 minuto
    retry: 2,
  });
}

// =====================================================
// HOOKS DE MUTACIÓN
// =====================================================

/**
 * Hook para crear un nuevo plan familiar
 */
export function useCreateFamilyPlan() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: CreateFamilyPlanData) => {
      const result = await familyPlanService.createFamilyPlan(data);
      if (!result.success) {
        throw new Error(result.error || 'Error al crear plan familiar');
      }
      return result.data!;
    },
    onSuccess: () => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: familyPlanQueryKeys.user() });
    },
  });
}

/**
 * Hook para unirse a un plan familiar
 */
export function useJoinFamilyPlan() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: JoinFamilyPlanData) => {
      const result = await familyPlanService.joinFamilyPlan(data);
      if (!result.success) {
        throw new Error(result.error || 'Error al unirse al plan familiar');
      }
      return result.data!;
    },
    onSuccess: () => {
      // Invalidar queries relacionadas
      queryClient.invalidateQueries({ queryKey: familyPlanQueryKeys.user() });
    },
  });
}

/**
 * Hook para agregar contacto de emergencia
 */
export function useAddEmergencyContact() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ planId, contact }: { 
      planId: string; 
      contact: Omit<EmergencyContact, 'id'> 
    }) => {
      const result = await familyPlanService.addEmergencyContact(planId, contact);
      if (!result.success) {
        throw new Error(result.error || 'Error al agregar contacto de emergencia');
      }
      return result.data!;
    },
    onSuccess: (_, { planId }) => {
      // Invalidar queries del plan específico
      queryClient.invalidateQueries({ queryKey: familyPlanQueryKeys.plan(planId) });
    },
  });
}

/**
 * Hook para agregar kit de emergencia
 */
export function useAddEmergencyKit() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ planId, kit }: { 
      planId: string; 
      kit: Omit<EmergencyKit, 'id' | 'total_items' | 'checked_items' | 'critical_items' | 'expiring_soon'> 
    }) => {
      const result = await familyPlanService.addEmergencyKit(planId, kit);
      if (!result.success) {
        throw new Error(result.error || 'Error al agregar kit de emergencia');
      }
      return result.data!;
    },
    onSuccess: (_, { planId }) => {
      // Invalidar queries del plan específico
      queryClient.invalidateQueries({ queryKey: familyPlanQueryKeys.plan(planId) });
    },
  });
}

/**
 * Hook para agregar punto de encuentro
 */
export function useAddMeetingPoint() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ planId, point }: { 
      planId: string; 
      point: Omit<MeetingPoint, 'id'> 
    }) => {
      const result = await familyPlanService.addMeetingPoint(planId, point);
      if (!result.success) {
        throw new Error(result.error || 'Error al agregar punto de encuentro');
      }
      return result.data!;
    },
    onSuccess: (_, { planId }) => {
      // Invalidar queries del plan específico
      queryClient.invalidateQueries({ queryKey: familyPlanQueryKeys.plan(planId) });
    },
  });
}

// =====================================================
// HOOKS COMPUESTOS
// =====================================================

/**
 * Hook para manejo completo de planes familiares
 */
export function useFamilyPlanManager() {
  const { isAuthenticated, user } = useAuth();
  const userPlansQuery = useUserFamilyPlans();
  const hasFamilyPlansQuery = useHasFamilyPlans();
  const primaryPlanQuery = usePrimaryFamilyPlan();

  const createPlanMutation = useCreateFamilyPlan();
  const joinPlanMutation = useJoinFamilyPlan();

  return {
    // Estado
    isAuthenticated,
    user,
    
    // Queries
    userPlans: userPlansQuery.data || [],
    primaryPlan: primaryPlanQuery.data,
    hasPlans: hasFamilyPlansQuery.data || false,
    
    // Estados de carga
    isLoading: userPlansQuery.isLoading || hasFamilyPlansQuery.isLoading,
    isLoadingPrimary: primaryPlanQuery.isLoading,
    
    // Estados de error
    error: userPlansQuery.error || hasFamilyPlansQuery.error || primaryPlanQuery.error,
    
    // Mutaciones
    createPlan: createPlanMutation.mutate,
    joinPlan: joinPlanMutation.mutate,
    
    // Estados de mutación
    isCreating: createPlanMutation.isPending,
    isJoining: joinPlanMutation.isPending,
    createError: createPlanMutation.error,
    joinError: joinPlanMutation.error,
    
    // Funciones de utilidad
    refetch: () => {
      userPlansQuery.refetch();
      hasFamilyPlansQuery.refetch();
      primaryPlanQuery.refetch();
    },
  };
}

/**
 * Hook para manejo de un plan familiar específico
 */
export function useFamilyPlanDetails(planId: string | null) {
  const planQuery = useFamilyPlan(planId, [
    'members', 
    'contacts', 
    'kits', 
    'meeting_points', 
    'evacuation_plans'
  ]);

  const addContactMutation = useAddEmergencyContact();
  const addKitMutation = useAddEmergencyKit();
  const addMeetingPointMutation = useAddMeetingPoint();

  return {
    // Datos del plan
    plan: planQuery.data,
    
    // Estados de carga
    isLoading: planQuery.isLoading,
    
    // Estados de error
    error: planQuery.error,
    
    // Mutaciones
    addContact: (contact: Omit<EmergencyContact, 'id'>) => 
      planId && addContactMutation.mutate({ planId, contact }),
    addKit: (kit: Omit<EmergencyKit, 'id' | 'total_items' | 'checked_items' | 'critical_items' | 'expiring_soon'>) => 
      planId && addKitMutation.mutate({ planId, kit }),
    addMeetingPoint: (point: Omit<MeetingPoint, 'id'>) => 
      planId && addMeetingPointMutation.mutate({ planId, point }),
    
    // Estados de mutación
    isAddingContact: addContactMutation.isPending,
    isAddingKit: addKitMutation.isPending,
    isAddingMeetingPoint: addMeetingPointMutation.isPending,
    
    // Errores de mutación
    addContactError: addContactMutation.error,
    addKitError: addKitMutation.error,
    addMeetingPointError: addMeetingPointMutation.error,
    
    // Funciones de utilidad
    refetch: planQuery.refetch,
  };
}
