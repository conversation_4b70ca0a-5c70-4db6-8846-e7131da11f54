# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Development Commands

### Core Development
- `npm start` - Start Expo development server
- `npm run android` - Start Android development
- `npm run ios` - Start iOS development  
- `npm run web` - Start web development

### Testing
- `npm test` - Run Jest tests in watch mode
- Test files are located in `__tests__/` directories throughout the codebase

## Project Architecture

### Tech Stack
- **Framework**: React Native with Expo (~53.0.20)
- **Navigation**: Expo Router v5 with file-based routing
- **State Management**: React Query (@tanstack/react-query) for server state
- **Backend**: Supabase integration with real-time subscriptions
- **Platform**: Cross-platform (iOS, Android, Web) with React Native 0.79.5

### Core Architecture Patterns

#### File-based Routing Structure
```
app/
├── _layout.tsx          # Root layout with QueryProvider and theme setup
├── +not-found.tsx       # 404 page
└── (tabs)/             # Tab navigation group
    ├── _layout.tsx     # Tab layout with HapticTab components
    ├── index.tsx       # Home screen (volcano dashboard)
    ├── explore.tsx     # Interactive map
    └── precursors.tsx  # Precursor monitoring
```

#### Component Organization
- **UI Components**: `/components/ui/` - Reusable design system components
  - `AccessibleButton.tsx` - Button variants (Primary, Secondary, Emergency)
  - `AccessibleText.tsx` - Typography with accessibility support  
  - `ModernIcon.tsx` - Lucide React Native icon system
  - `TabBarBackground.tsx` - Platform-specific tab styling
- **Feature Components**: `/components/` - Domain-specific components
  - `VolcanoStatus.tsx` - Real-time volcano monitoring
  - `InteractiveMap.tsx` - Safety zones and evacuation routes
  - `AlertSystem.tsx` - Emergency notification system
  - `ConnectionStatus.tsx` - Network connectivity indicator

#### Service Layer Architecture
- **API Service** (`/services/api.ts`): 
  - Axios-based HTTP client with interceptors
  - Structured endpoints for volcano alerts, safety zones, location reporting
  - Offline sync capabilities with batch operations
- **WebSocket Service** (`/services/websocket.ts`): Real-time volcano data updates
- **Notifications** (`/services/notifications.ts`): Push notification management
- **Device Management** (`/services/deviceId.ts`): Anonymous device tracking
- **Supabase Integration** (`/services/supabase.ts`): Database and real-time subscriptions

#### State Management Strategy
- **React Query**: Server state, caching, background updates, offline support
- **Custom Hooks** (`/hooks/`):
  - `useApi.ts` - Unified API data fetching
  - `useNotifications.ts` - Notification permissions and state
  - `useColorScheme.ts` - Theme detection with platform variants

#### Theme and Design System
- **Colors** (`/constants/Colors.ts`): 
  - WCAG AA compliant color system
  - Emergency alert level colors (Green→Yellow→Orange→Red→Purple)
  - Dark/light mode support
- **Layout Constants** (`/constants/Layout.ts`): Spacing, border radius, typography scales
- **Accessibility**: Full accessibility support with semantic markup and screen reader compatibility

#### Key Architectural Decisions
1. **Emergency-First Design**: Critical features (emergency button, alerts) are prominently accessible
2. **Offline Capability**: React Query with background sync, local data persistence
3. **Real-time Updates**: WebSocket integration for live volcano monitoring data
4. **Cross-platform Consistency**: Shared codebase with platform-specific optimizations
5. **Type Safety**: Full TypeScript implementation with strict mode enabled
6. **Accessibility Standards**: WCAG compliance throughout the interface
7. **Modular Architecture**: Clear separation between UI, business logic, and data layers

### Import Path Configuration
- Uses `@/*` alias pointing to project root (configured in tsconfig.json)
- Consistent import organization: external dependencies → internal modules → relative imports

### Development Patterns
- **Component Pattern**: Functional components with TypeScript interfaces
- **Error Handling**: Comprehensive error boundaries and user-friendly fallbacks  
- **Performance**: Optimized with React Query caching and lazy loading
- **Testing**: Jest with React Test Renderer, component and service layer tests
- **Platform Adaptation**: Platform.select() for platform-specific implementations