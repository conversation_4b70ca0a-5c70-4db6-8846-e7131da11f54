# PRD: VolkanApp - Product Requirements Document

**Análisis de Pitch y Hoja de Ruta para Cumplimiento Total**

---

## 🎯 Executive Summary

VolkanApp es una aplicación móvil de monitoreo volcánico y alertas de emergencia que busca **democratizar el acceso a información representativa** y hacer el sistema de alertas **más inteligente e inclusivo**. 

**Estado Actual**: Base técnica sólida (7/10) con arquitectura moderna preparada para escalabilidad.
**Objetivo**: Cumplimiento total del pitch con funcionalidades críticas para poblaciones vulnerables.

---

## 📊 Análisis del Pitch vs Capacidades Actuales

### Problema Identificado
- **50,000 personas** en zona de riesgo durante temporada alta en Pucón
- **22% adultos con discapacidad**, **13% adultos mayores**, **30% población migrante haitiana**
- **Sistemas de alerta tradicionales insuficientes** (sirenas ambiguas, SMS sin contexto)
- **Colapso de redes celulares** durante crisis
- **Pérdidas económicas**: $2 mil millones de pesos diarios en Pucón durante alertas

### Solución Propuesta: VolkanApp
Una aplicación que provee **información en tiempo real**, **pronósticos personalizados**, y **orientación clara** utilizando Big Data y tecnología móvil para comunicación bidireccional efectiva.

---

## ✅ Funcionalidades IMPLEMENTADAS

### 1. **Monitoreo en Tiempo Real** ✅
- **Dashboard volcánico** con estado actual de Volcán Villarrica
- **WebSocket integration** para actualizaciones en vivo
- **Sistema de alertas** con niveles de riesgo (Verde→Amarillo→Naranja→Rojo→Púrpura)
- **Ubicación**: `app/(tabs)/index.tsx` - `VolcanoStatus` component

### 2. **Geolocalización y Mapas de Riesgo** ✅
- **GPS integrado** para ubicación exacta del usuario
- **Mapa interactivo** con zonas de seguridad oficiales
- **Cálculo de distancia** al volcán en tiempo real
- **Rutas de evacuación** estandarizadas
- **Ubicación**: `app/(tabs)/explore.tsx` - `InteractiveMap` component

### 3. **Funcionalidad Offline** ✅ (Fundación)
- **React Query caching** para datos críticos
- **Background sync** cuando se recupera conexión
- **Arquitectura offline-first** implementada
- **Ubicación**: Service layer con capacidades de sync

### 4. **Accesibilidad Completa** ✅
- **Cumplimiento WCAG AA** en toda la interfaz
- **Screen reader support** con semantic markup
- **Navegación por teclado** optimizada
- **Componentes accesibles**: `AccessibleText`, `AccessibleButton`
- **Ubicación**: `components/ui/AccessibleText.tsx`, `components/ui/AccessibleButton.tsx`

### 5. **Arquitectura Multiplataforma** ✅
- **React Native + Expo** para iOS, Android, Web
- **Diseño responsivo** para diferentes tamaños de pantalla
- **Optimización cross-platform** con código compartido
- **Ubicación**: Configuración en `app/_layout.tsx`

### 6. **Sistema de Notificaciones** ✅
- **Push notifications** para alertas críticas
- **Notificaciones locales** para recordatorios
- **Integración con permisos** del sistema
- **Ubicación**: `services/notifications.ts`

### 7. **Diseño Moderno** ✅
- **UI inspirada en apps de viaje** modernas
- **Sistema de gradientes** y tarjetas elevadas
- **Iconografía moderna** con Lucide React Native
- **Ubicación**: `components/ui/ModernCard.tsx`, `components/ui/HeroCard.tsx`

---

## ❌ Funcionalidades CRÍTICAS FALTANTES

### 1. **Sistema Multiidioma** 🌍
**Prioridad: CRÍTICA**
**Impacto**: 30% población migrante haitiana + turistas internacionales

#### Requisitos:
- **Español** (implementado) ✅
- **Inglés** para turistas internacionales
- **Creole Haitiano** para población migrante
- **Detección automática** de idioma del dispositivo
- **Selector manual** de idioma en configuración

#### Implementación:
```typescript
// Estructura propuesta
i18n/
├── es.json     // Español (actual)
├── en.json     // English
├── ht.json     // Kreyòl Ayisyen (Haitian Creole)
└── index.ts    // i18n configuration
```

### 2. **Navegación por Voz** 🔊
**Prioridad: CRÍTICA**
**Impacto**: Accesibilidad para discapacitados visuales y analfabetos

#### Requisitos:
- **Lectura de alertas** automática por voz
- **Comandos de voz** para navegación básica
- **Integración nativa** con VoiceOver/TalkBack
- **Soporte multiidioma** en síntesis de voz
- **Activación por gestos** o palabras clave

#### Funcionalidades:
- "Leer estado actual del volcán"
- "¿Dónde está la ruta de evacuación más cercana?"
- "Activar modo emergencia"
- "Reproducir última alerta"

### 3. **Visualización Avanzada de Big Data** 📊
**Prioridad: ALTA**
**Impacto**: Pronósticos personalizados y probabilidades de riesgo

#### Requisitos Actuales vs Necesarios:
- **Actual**: Análisis básico de precursores ✅
- **Necesario**: Visualizaciones probabilísticas dinámicas
- **Necesario**: Mapas de calor de riesgo en tiempo real
- **Necesario**: Pronósticos personalizados por ubicación
- **Necesario**: Tendencias históricas y patrones

#### Mejoras Propuestas:
```typescript
// Nuevos componentes necesarios
components/
├── BigDataDashboard.tsx      // Dashboard probabilístico
├── RiskHeatMap.tsx          // Mapa de calor de riesgo
├── PersonalizedForecast.tsx // Pronósticos por ubicación
└── HistoricalTrends.tsx     // Análisis histórico
```

### 4. **Planes Familiares de Emergencia** 👨‍👩‍👧‍👦
**Prioridad: ALTA**
**Impacto**: Cultura de prevención y preparación familiar

#### Funcionalidades Requeridas:
- **Gestión de contactos** de emergencia familiares
- **Lista de kit de emergencia** personalizable y verificable
- **Puntos de encuentro** familiares preestablecidos
- **Planes de evacuación** específicos por familia
- **Protocolos post-erupción** y recuperación
- **Sincronización familiar** entre dispositivos

#### Estructura Propuesta:
```typescript
// Nuevas pantallas y componentes
app/(tabs)/family-plan.tsx           // Pantalla principal
components/
├── EmergencyContacts.tsx           // Gestión de contactos
├── EmergencyKit.tsx               // Lista kit emergencia
├── FamilyMeetingPoints.tsx        // Puntos de encuentro
├── EvacuationPlan.tsx             // Plan evacuación
└── PostEruptionProtocol.tsx       // Protocolos post-erupción
```

---

## 🏢 Funcionalidades de NEGOCIO (Monetización)

### 1. **Versión Empresarial (VolkanApp Pro)** 💼
**Prioridad: MEDIA**
**Target**: Empresas con empleados en zonas de riesgo

#### Funcionalidades Pro:
- **Dashboard empresarial** con gestión de empleados
- **Alertas corporativas** personalizadas
- **Estrategias comerciales** durante diferentes niveles de alerta
- **Reportes analíticos** de riesgo y exposición
- **API empresarial** para integración con sistemas internos
- **Gestión de flotas** y ubicación de vehículos empresariales

#### Casos de Uso:
- Hoteles con huéspedes en zona de riesgo
- Empresas turísticas (ski, trekking, termas)
- Constructoras con obras en la zona
- Servicios de transporte y logística

### 2. **Modelo de Ingresos** 💰

#### Freemium Model:
- **Versión Gratuita**: Funcionalidades básicas de seguridad
- **Versión Pro Individual**: $2.990 CLP/mes - Funciones avanzadas
- **Versión Pro Empresarial**: $29.990 CLP/mes - Gestión corporativa

#### Revenue Streams:
- **Suscripciones Premium**: 70% del revenue proyectado
- **Publicidad Contextual**: 20% del revenue (no intrusiva)
- **Contratos Gubernamentales**: 10% del revenue (integración oficial)

---

## 🏛️ Integración INSTITUCIONAL

### 1. **Protocolos SENAPRED y Municipios** 
**Prioridad: ALTA**
**Impacto**: Legitimidad y fuente oficial de información

#### Requisitos de Integración:
- **API oficial** SENAPRED para alertas gubernamentales
- **Sincronización automática** con protocolos municipales
- **Validación científica** con SERNAGEOMIN
- **Canal oficial** de comunicación de emergencias
- **Certificación gubernamental** como app oficial

#### Beneficios:
- Fuente única de verdad para alertas
- Reducción de información contradictoria
- Respaldo institucional y confianza ciudadana
- Posibilidad de financiamiento público

### 2. **Escalabilidad Multi-Riesgo** 🌪️
**Prioridad: MEDIA-BAJA**
**Visión**: Plataforma nacional de gestión de riesgos

#### Expansión Propuesta:
- **Fase 1**: Volcanes (actual)
- **Fase 2**: Terremotos y tsunamis
- **Fase 3**: Incendios forestales
- **Fase 4**: Aluviones y deslizamientos
- **Fase 5**: Eventos climáticos extremos

---

## 📱 Especificaciones Técnicas Adicionales

### 1. **Arquitectura de Internacionalización**
```typescript
// Implementación i18n con react-native-i18n
utils/
├── i18n.ts                 // Configuración principal
├── languageDetector.ts     // Detección automática
└── translations/
    ├── es/                 // Español
    ├── en/                 // English  
    └── ht/                 // Kreyòl Ayisyen
```

### 2. **Arquitectura de Voz**
```typescript
// Integración con expo-speech y expo-voice
services/
├── voiceNavigation.ts      // Navegación por voz
├── textToSpeech.ts        // Síntesis de voz
├── speechRecognition.ts   // Reconocimiento de voz
└── voiceCommands.ts       // Comandos predefinidos
```

### 3. **Big Data Backend Requirements**
```typescript
// APIs adicionales necesarias
services/
├── probabilisticForecast.ts // Pronósticos probabilísticos
├── riskAnalytics.ts        // Análisis de riesgo avanzado
├── historicalData.ts       // Datos históricos y tendencias
└── personalizedRisk.ts     // Riesgo personalizado por ubicación
```

---

## 🚀 Roadmap de Implementación

### **FASE 1: Inclusión y Accesibilidad** (Prioridad CRÍTICA)
**Timeline: 4-6 semanas**

1. **Sistema Multiidioma** (2 semanas)
   - Implementar i18n con react-i18next
   - Traducir interfaz completa a inglés y creole
   - Detección automática de idioma

2. **Navegación por Voz** (2-3 semanas)
   - Integrar expo-speech para TTS
   - Implementar comandos de voz básicos
   - Testing con usuarios con discapacidad visual

3. **Mejoras de Offline** (1 semana)
   - Mapas offline completos
   - Protocolos de emergencia sin conexión
   - Contactos de emergencia offline

### **FASE 2: Big Data y Personalización** (Prioridad ALTA)
**Timeline: 6-8 semanas**

1. **Visualizaciones Avanzadas** (3-4 semanas)
   - Dashboard probabilístico
   - Mapas de calor de riesgo
   - Pronósticos personalizados

2. **Planes Familiares** (3-4 semanas)
   - Sistema de gestión familiar
   - Sincronización entre dispositivos
   - Kit de emergencia digital

### **FASE 3: Monetización y Escalabilidad** (Prioridad MEDIA)
**Timeline: 8-12 semanas**

1. **Versión Empresarial** (6-8 semanas)
   - Dashboard corporativo
   - API empresarial
   - Sistema de suscripciones

2. **Integración Gubernamental** (4-6 semanas)
   - API SENAPRED
   - Certificación oficial
   - Protocolos municipales

---

## 💡 Recomendación de Implementación Inmediata

Para maximizar el impacto y cumplir con el pitch, recomiendo comenzar con:

### **1. Sistema Multiidioma** 🌍
- **Impacto inmediato** en el 30% población migrante
- **Diferenciador competitivo** vs sistemas actuales
- **Base técnica simple** de implementar

### **2. Navegación por Voz** 🔊  
- **Innovación disruptiva** en alertas de emergencia
- **Accesibilidad real** para poblaciones vulnerables
- **Valor agregado único** vs competencia

### **3. Planes Familiares** 👨‍👩‍👧‍👦
- **Engagement alto** y retención de usuarios
- **Cultura de prevención** tangible
- **Funcionalidad viral** (familias invitan familias)

---

## 📊 Métricas de Éxito

### KPIs Técnicos:
- **Adopción multiidioma**: >40% usuarios usan idiomas alternativos
- **Uso de voz**: >60% usuarios activan funciones de voz
- **Tiempo offline**: App funcional >95% del tiempo sin conexión

### KPIs de Negocio:
- **Conversión Premium**: >15% usuarios pagan suscripción
- **Retención familiar**: >80% familias completan planes de emergencia
- **Satisfacción**: >4.5/5 rating en stores

### KPIs de Impacto Social:
- **Reducción tiempo evacuación**: <50% vs métodos tradicionales
- **Comprensión de alertas**: >90% usuarios entienden niveles de riesgo
- **Preparación familiar**: >70% familias tienen kit de emergencia completo

---

## 🎯 Conclusión

VolkanApp tiene **excelente base técnica** y cumple con 7/10 requisitos del pitch. Las funcionalidades faltantes son implementables y agregarán **valor diferenciador único** en el mercado de gestión de emergencias.

**Próximos pasos recomendados**:
1. ✅ Aprobar PRD y roadmap
2. 🚀 Iniciar Fase 1 con sistema multiidioma
3. 👥 Formar equipo especializado en accesibilidad
4. 🤝 Iniciar conversaciones con SENAPRED
5. 💰 Preparar modelo de negocios para inversión

**VolkanApp está preparada para transformar la gestión de emergencias en Chile** 🇨🇱🌋