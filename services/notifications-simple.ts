/**
 * 🌋 Volcano App Mobile - Servicio de Notificaciones Simplificado
 * Versión sin WebSockets para compatibilidad con React Native
 */

import Constants from 'expo-constants';
import * as Device from 'expo-device';
import * as Haptics from 'expo-haptics';
import * as Notifications from 'expo-notifications';
import { Platform, Vibration } from 'react-native';

// =====================================================
// TIPOS Y INTERFACES
// =====================================================

export interface NotificationData {
  type: 'volcano_alert' | 'zone_update' | 'system_status' | 'test';
  alertId?: string;
  alertLevel?: string;
  volcanoName?: string;
  volcanoLat?: number;
  volcanoLng?: number;
  zoneId?: string;
  zoneName?: string;
  zoneType?: string;
  action?: string;
  vibrationPattern?: number[];
  timestamp: string;
}

export interface NotificationPermissions {
  status: 'granted' | 'denied' | 'undetermined';
  canAskAgain: boolean;
  canScheduleExact: boolean;
}

// =====================================================
// CONFIGURACIÓN DE NOTIFICACIONES
// =====================================================

// Configurar el comportamiento de las notificaciones
Notifications.setNotificationHandler({
  handleNotification: async (notification) => {
    const data = notification.request.content.data as NotificationData;
    
    // Configurar según el tipo de notificación
    switch (data.type) {
      case 'volcano_alert':
        return {
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: true,
        };
      case 'zone_update':
        return {
          shouldShowAlert: true,
          shouldPlaySound: false,
          shouldSetBadge: false,
        };
      case 'system_status':
        return {
          shouldShowAlert: false,
          shouldPlaySound: false,
          shouldSetBadge: false,
        };
      case 'test':
        return {
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: false,
        };
      default:
        return {
          shouldShowAlert: true,
          shouldPlaySound: false,
          shouldSetBadge: false,
        };
    }
  },
});

// =====================================================
// SERVICIO DE NOTIFICACIONES SIMPLIFICADO
// =====================================================

class SimpleNotificationService {
  private isInitialized = false;
  private pushToken: string | null = null;
  private notificationListener: any = null;
  private responseListener: any = null;

  /**
   * Inicializar el servicio de notificaciones
   */
  async initialize(): Promise<NotificationPermissions> {
    try {
      console.log('🔔 Initializing simple notification service...');

      // Verificar si es un dispositivo físico
      if (!Device.isDevice) {
        console.warn('⚠️ Push notifications only work on physical devices');
        return {
          status: 'denied',
          canAskAgain: false,
          canScheduleExact: false
        };
      }

      // Solicitar permisos
      const permissions = await this.requestPermissions();
      
      if (permissions.status === 'granted') {
        // Obtener token de push
        await this.registerForPushNotifications();
        
        // Configurar listeners
        this.setupNotificationListeners();

        this.isInitialized = true;
        console.log('✅ Simple notification service initialized successfully');
      } else {
        console.warn('⚠️ Notification permissions not granted');
      }

      return permissions;

    } catch (error) {
      console.error('❌ Failed to initialize simple notification service:', error);
      throw error;
    }
  }

  /**
   * Solicitar permisos de notificaciones
   */
  async requestPermissions(): Promise<NotificationPermissions> {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      // En Android, también solicitar permisos de notificaciones exactas
      let canScheduleExact = true;
      if (Platform.OS === 'android') {
        try {
          const { status: exactStatus } = await Notifications.getPermissionsAsync();
          canScheduleExact = exactStatus === 'granted';
        } catch (error) {
          console.warn('Could not check exact notification permissions:', error);
        }
      }

      return {
        status: finalStatus as 'granted' | 'denied' | 'undetermined',
        canAskAgain: finalStatus !== 'denied',
        canScheduleExact
      };

    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return {
        status: 'denied',
        canAskAgain: false,
        canScheduleExact: false
      };
    }
  }

  /**
   * Registrar para notificaciones push
   */
  async registerForPushNotifications(): Promise<string | null> {
    try {
      // Obtener projectId desde la configuración
      const projectId = Constants.expoConfig?.extra?.eas?.projectId || 'volcano-app-mobile';

      console.log('📱 Attempting to get push token with projectId:', projectId);

      const token = await Notifications.getExpoPushTokenAsync({
        projectId: projectId,
      });

      this.pushToken = token.data;
      console.log('📱 Push token obtained:', this.pushToken);

      return this.pushToken;

    } catch (error) {
      console.error('Error getting push token:', error);
      console.warn('⚠️ Push notifications may not work in Expo Go. Use development build for full functionality.');
      return null;
    }
  }

  /**
   * Configurar listeners de notificaciones
   */
  private setupNotificationListeners(): void {
    // Listener para notificaciones recibidas mientras la app está en primer plano
    this.notificationListener = Notifications.addNotificationReceivedListener(
      (notification) => {
        console.log('🔔 Notification received:', notification);
        this.handleNotificationReceived(notification);
      }
    );

    // Listener para cuando el usuario toca una notificación
    this.responseListener = Notifications.addNotificationResponseReceivedListener(
      (response) => {
        console.log('👆 Notification tapped:', response);
        this.handleNotificationResponse(response);
      }
    );
  }

  /**
   * Manejar notificación recibida
   */
  private handleNotificationReceived(notification: Notifications.Notification): void {
    const data = notification.request.content.data as NotificationData;

    // Efectos hápticos y vibración según el tipo
    this.triggerHapticFeedback(data);
  }

  /**
   * Manejar respuesta a notificación (cuando el usuario la toca)
   */
  private handleNotificationResponse(response: Notifications.NotificationResponse): void {
    const data = response.notification.request.content.data as NotificationData;

    // Navegar a la pantalla apropiada según el tipo de notificación
    switch (data.type) {
      case 'volcano_alert':
        console.log('Navigate to alerts screen');
        break;
      case 'zone_update':
        console.log('Navigate to map screen');
        break;
      default:
        console.log('Default notification action');
    }
  }

  /**
   * Activar feedback háptico y vibración
   */
  private triggerHapticFeedback(data: NotificationData): void {
    try {
      // Vibración personalizada si está disponible
      if (data.vibrationPattern && data.vibrationPattern.length > 0) {
        if (Platform.OS === 'android') {
          Vibration.vibrate(data.vibrationPattern);
        } else {
          // En iOS, usar patrones predefinidos
          this.triggerIOSHaptics(data.type);
        }
      } else {
        // Vibración por defecto según el tipo
        this.triggerDefaultVibration(data.type);
      }

    } catch (error) {
      console.error('Error triggering haptic feedback:', error);
    }
  }

  /**
   * Activar hápticos en iOS
   */
  private triggerIOSHaptics(type: string): void {
    if (Platform.OS !== 'ios') return;

    switch (type) {
      case 'volcano_alert':
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        break;
      case 'zone_update':
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
        break;
      case 'test':
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        break;
      default:
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
  }

  /**
   * Activar vibración por defecto
   */
  private triggerDefaultVibration(type: string): void {
    switch (type) {
      case 'volcano_alert':
        Vibration.vibrate([0, 1000, 500, 1000, 500, 1000]); // Patrón de emergencia
        break;
      case 'zone_update':
        Vibration.vibrate([0, 300, 100, 300]); // Patrón suave
        break;
      case 'test':
        Vibration.vibrate(200); // Vibración simple
        break;
      default:
        Vibration.vibrate(100);
    }
  }

  /**
   * Mostrar notificación local
   */
  async showLocalNotification(
    title: string,
    body: string,
    data?: NotificationData
  ): Promise<string> {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: data || { type: 'test', timestamp: new Date().toISOString() },
          sound: true,
        },
        trigger: null, // Mostrar inmediatamente
      });

      return notificationId;

    } catch (error) {
      console.error('Error showing local notification:', error);
      throw error;
    }
  }

  /**
   * Limpiar listeners
   */
  cleanup(): void {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
    }
  }

  /**
   * Obtener token de push
   */
  getPushToken(): string | null {
    return this.pushToken;
  }

  /**
   * Verificar si está inicializado
   */
  isReady(): boolean {
    return this.isInitialized;
  }
}

// =====================================================
// EXPORTACIONES
// =====================================================

export const simpleNotificationService = new SimpleNotificationService();
export default simpleNotificationService;
