/**
 * 🌋 Volcano App - Pantalla de Análisis de Precursores
 * Pantalla dedicada al análisis avanzado de precursores volcánicos
 */

import React, { useState, useCallback } from 'react';
import {
  StyleSheet,
  View,
  ScrollView,
  Alert,
  Platform,
} from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';
import { Spacing, BorderRadius } from '@/constants/Layout';
import { AccessibleText } from '@/components/ui/AccessibleText';
import { PrimaryButton, SecondaryButton } from '@/components/ui/AccessibleButton';
import { ModernHeader } from '@/components/ui/ModernHeader';
import { LanguageSelector } from '@/components/ui/LanguageSelector';
import { AlertIcon } from '@/components/ui/ModernIcon';
import { useI18n } from '@/hooks/useI18n';
import PrecursorAcceleration from '@/components/PrecursorAcceleration';
import {
  DatosPrecursor,
  ResultadoAlerta,
  DATOS_PRUEBA,
  UmbralesAlerta,
} from '@/types/precursor';

/**
 * Datos de ejemplo adicionales para demostración
 */
const DATOS_EJEMPLOS: DatosPrecursor[] = [
  DATOS_PRUEBA,
  {
    valores: [1, 1, 2, 2, 3, 4, 5, 7, 10, 15, 22],
    tipo: 'Deformación del Suelo',
    unidad: 'mm/día',
    volcan: 'Volcán Villarrica',
  },
  {
    valores: [5, 6, 7, 8, 10, 13, 17, 22, 28, 35, 43],
    tipo: 'Emisiones de SO2',
    unidad: 'toneladas/día',
    volcan: 'Volcán Villarrica',
  },
  {
    valores: [0.1, 0.2, 0.3, 0.5, 0.8, 1.3, 2.1, 3.4, 5.5, 8.9, 14.4],
    tipo: 'Temperatura Fumarolas',
    unidad: '°C incremento',
    volcan: 'Volcán Villarrica',
  },
];

export default function PrecursorsScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t } = useI18n();

  // Estado de la pantalla
  const [datosActuales, setDatosActuales] = useState<DatosPrecursor>(DATOS_PRUEBA);
  const [ejemploSeleccionado, setEjemploSeleccionado] = useState<number>(0);
  const [umbralesPersonalizados, setUmbralesPersonalizados] = useState<UmbralesAlerta>({
    verde: 1,
    amarillo: 5,
    rojo: 5,
  });

  // Manejar alertas del componente
  const handleAlerta = useCallback((resultado: ResultadoAlerta) => {
    const titulo = `${t('alerts.levels.' + resultado.nivel.toLowerCase(), { defaultValue: 'Alerta' })}: ${resultado.nivel}`;
    const mensaje = `${resultado.mensaje}\n\n${t('common.detected', { defaultValue: 'Valor detectado' })}: ${resultado.valor?.toFixed(2) || 'N/A'}`;

    if (Platform.OS === 'web') {
      alert(`${titulo}\n\n${mensaje}`);
    } else {
      Alert.alert(titulo, mensaje, [
        { text: t('common.ok'), style: 'default' },
      ]);
    }
  }, []);

  // Cambiar ejemplo de datos
  const cambiarEjemplo = useCallback((indice: number) => {
    if (indice >= 0 && indice < DATOS_EJEMPLOS.length) {
      setEjemploSeleccionado(indice);
      setDatosActuales(DATOS_EJEMPLOS[indice]);
    }
  }, []);

  // Renderizar selector de ejemplos
  const renderSelectorEjemplos = () => (
    <View style={styles.selectorContainer}>
      <AccessibleText variant="h4" style={styles.selectorTitulo}>
        {t('precursors.dataExamples')}
      </AccessibleText>
      <ScrollView 
        horizontal 
        showsHorizontalScrollIndicator={false}
        style={styles.selectorScroll}
      >
        {DATOS_EJEMPLOS.map((ejemplo, indice) => (
          <SecondaryButton
            key={indice}
            onPress={() => cambiarEjemplo(indice)}
            style={[
              styles.botonEjemplo,
              ejemploSeleccionado === indice && styles.botonEjemploActivo
            ]}
          >
            {ejemplo.tipo}
          </SecondaryButton>
        ))}
      </ScrollView>
    </View>
  );

  // Renderizar información del dataset actual
  const renderInfoDataset = () => (
    <View style={styles.infoContainer}>
      <AccessibleText variant="h4" style={styles.infoTitulo}>
        {t('precursors.currentDataset')}
      </AccessibleText>
      <View style={styles.infoGrid}>
        <View style={styles.infoItem}>
          <AccessibleText variant="caption">{t('precursors.type')}:</AccessibleText>
          <AccessibleText variant="body">{datosActuales.tipo}</AccessibleText>
        </View>
        <View style={styles.infoItem}>
          <AccessibleText variant="caption">{t('precursors.unit')}:</AccessibleText>
          <AccessibleText variant="body">{datosActuales.unidad}</AccessibleText>
        </View>
        <View style={styles.infoItem}>
          <AccessibleText variant="caption">{t('precursors.dataPoints')}:</AccessibleText>
          <AccessibleText variant="body">{datosActuales.valores.length}</AccessibleText>
        </View>
        <View style={styles.infoItem}>
          <AccessibleText variant="caption">{t('precursors.volcano')}:</AccessibleText>
          <AccessibleText variant="body">{datosActuales.volcan}</AccessibleText>
        </View>
      </View>
    </View>
  );

  return (
    <View style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Modern Header */}
      <ModernHeader
        title={t('precursors.title')}
        subtitle={t('precursors.subtitle')}
        leftIcon={<AlertIcon size={24} color="white" strokeWidth={2.5} />}
        rightIcon={<LanguageSelector compact showLabel={false} />}
        gradient={true}
        gradientColors={colors.gradientVolcano}
      />
      
      <ScrollView 
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
      >

        {/* Selector de ejemplos */}
        {renderSelectorEjemplos()}

        {/* Información del dataset */}
        {renderInfoDataset()}

        {/* Componente principal de análisis */}
        <View style={styles.analisisContainer}>
          <PrecursorAcceleration
            datos={datosActuales}
            umbrales={umbralesPersonalizados}
            onAlerta={handleAlerta}
            titulo="Análisis de Aceleración"
            altura={250}
            mostrarControles={true}
          />
        </View>

        {/* Información adicional */}
        <View style={styles.infoAdicional}>
          <AccessibleText variant="h4" style={styles.infoTitulo}>
            ℹ️ {t('precursors.howItWorks')}
          </AccessibleText>
          <AccessibleText variant="body" style={styles.infoTexto}>
            {t('precursors.explanation', {
              bold: (str: string) => str,
              interpolation: { escapeValue: false }
            })}
          </AccessibleText>
          <AccessibleText variant="body" style={styles.infoTexto}>
            • <AccessibleText variant="body" style={styles.textoVerde}>{t('precursors.greenLevel')}</AccessibleText>
          </AccessibleText>
          <AccessibleText variant="body" style={styles.infoTexto}>
            • <AccessibleText variant="body" style={styles.textoAmarillo}>{t('precursors.yellowLevel')}</AccessibleText>
          </AccessibleText>
          <AccessibleText variant="body" style={styles.infoTexto}>
            • <AccessibleText variant="body" style={styles.textoRojo}>{t('precursors.redLevel')}</AccessibleText>
          </AccessibleText>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },

  scrollView: {
    flex: 1,
  },

  scrollContent: {
    padding: Spacing.md,
  },

  selectorContainer: {
    marginBottom: Spacing.lg,
  },

  selectorTitulo: {
    marginBottom: Spacing.md,
    textAlign: 'center',
  },

  selectorScroll: {
    flexGrow: 0,
  },

  botonEjemplo: {
    marginRight: Spacing.sm,
    paddingHorizontal: Spacing.md,
    minWidth: 120,
  },

  botonEjemploActivo: {
    backgroundColor: '#3b82f6',
  },

  infoContainer: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    marginBottom: Spacing.lg,
  },

  infoTitulo: {
    textAlign: 'center',
    marginBottom: Spacing.md,
    fontWeight: '600',
  },

  infoGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },

  infoItem: {
    width: '48%',
    marginBottom: Spacing.sm,
  },

  analisisContainer: {
    marginBottom: Spacing.xl,
  },

  infoAdicional: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: BorderRadius.lg,
    padding: Spacing.md,
    marginBottom: Spacing.xl,
  },

  infoTexto: {
    marginBottom: Spacing.sm,
    lineHeight: 20,
  },

  textoBold: {
    fontWeight: 'bold',
  },

  textoVerde: {
    color: '#22c55e',
    fontWeight: 'bold',
  },

  textoAmarillo: {
    color: '#eab308',
    fontWeight: 'bold',
  },

  textoRojo: {
    color: '#ef4444',
    fontWeight: 'bold',
  },
});
