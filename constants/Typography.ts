/**
 * Volcano App Typography System
 * Designed for maximum accessibility and readability
 * Supports dyslexia-friendly options and scalable text
 */

import { Platform } from 'react-native';

// Font families optimized for accessibility
export const FontFamilies = {
  // Primary: High readability, dyslexia-friendly
  primary: Platform.select({
    ios: 'SF Pro Display',
    android: 'Roboto',
    default: 'system',
  }),
  
  // Secondary: For body text
  secondary: Platform.select({
    ios: 'SF Pro Text',
    android: 'Roboto',
    default: 'system',
  }),
  
  // Monospace: For technical data
  mono: Platform.select({
    ios: 'SF Mono',
    android: 'Roboto Mono',
    default: 'monospace',
  }),
};

// Font weights
export const FontWeights = {
  light: '300' as const,
  regular: '400' as const,
  medium: '500' as const,
  semibold: '600' as const,
  bold: '700' as const,
  extrabold: '800' as const,
};

// Base font sizes (scalable)
export const FontSizes = {
  xs: 12,
  sm: 14,
  base: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 30,
  '4xl': 36,
  '5xl': 48,
  '6xl': 60,
};

// Line heights for optimal readability
export const LineHeights = {
  tight: 1.2,
  normal: 1.4,
  relaxed: 1.6,
  loose: 1.8,
};

// Letter spacing for accessibility
export const LetterSpacing = {
  tight: -0.5,
  normal: 0,
  wide: 0.5,
  wider: 1,
};

// Typography scale for consistent hierarchy
export const Typography = {
  // Headlines
  h1: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes['4xl'],
    fontWeight: FontWeights.bold,
    lineHeight: LineHeights.tight,
    letterSpacing: LetterSpacing.tight,
  },
  
  h2: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.bold,
    lineHeight: LineHeights.tight,
    letterSpacing: LetterSpacing.normal,
  },
  
  h3: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.semibold,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacing.normal,
  },
  
  h4: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semibold,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacing.normal,
  },
  
  // Body text
  bodyLarge: {
    fontFamily: FontFamilies.secondary,
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.regular,
    lineHeight: LineHeights.relaxed,
    letterSpacing: LetterSpacing.normal,
  },
  
  body: {
    fontFamily: FontFamilies.secondary,
    fontSize: FontSizes.base,
    fontWeight: FontWeights.regular,
    lineHeight: LineHeights.relaxed,
    letterSpacing: LetterSpacing.normal,
  },
  
  bodySmall: {
    fontFamily: FontFamilies.secondary,
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.regular,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacing.normal,
  },
  
  // Interactive elements
  button: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semibold,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacing.wide,
  },
  
  buttonLarge: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semibold,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacing.wide,
  },
  
  // Navigation
  tab: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacing.normal,
  },
  
  // Emergency/Alert text
  alert: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.bold,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacing.wide,
  },
  
  alertLarge: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.extrabold,
    lineHeight: LineHeights.tight,
    letterSpacing: LetterSpacing.wide,
  },
  
  // Captions and metadata
  caption: {
    fontFamily: FontFamilies.secondary,
    fontSize: FontSizes.xs,
    fontWeight: FontWeights.regular,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacing.normal,
  },
  
  // Technical data
  code: {
    fontFamily: FontFamilies.mono,
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.regular,
    lineHeight: LineHeights.normal,
    letterSpacing: LetterSpacing.normal,
  },
};

// Accessibility scaling factors
export const AccessibilityScales = {
  small: 0.85,
  normal: 1.0,
  large: 1.15,
  extraLarge: 1.3,
  huge: 1.5,
};

// Helper function to scale typography
export const scaleTypography = (style: any, scale: number = 1) => ({
  ...style,
  fontSize: style.fontSize * scale,
});

// Emergency mode typography (ultra-simplified)
export const EmergencyTypography = {
  title: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes['5xl'],
    fontWeight: FontWeights.extrabold,
    lineHeight: LineHeights.tight,
    letterSpacing: LetterSpacing.wider,
  },
  
  instruction: {
    fontFamily: FontFamilies.primary,
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.bold,
    lineHeight: LineHeights.relaxed,
    letterSpacing: LetterSpacing.wide,
  },
};
