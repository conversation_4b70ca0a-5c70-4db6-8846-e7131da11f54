/**
 * AlertSystem Component
 * Sistema de alertas inteligente y contextual para emergencias volcánicas
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  Animated,
  Vibration,
  Platform,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors, AlertLevel, AlertLevels } from '@/constants/Colors';
import { Layout, Spacing, BorderRadius, ZIndex } from '@/constants/Layout';
import { 
  Heading2, 
  Heading3, 
  BodyText, 
  AlertText,
  AlertTextLarge 
} from './ui/AccessibleText';
import { 
  PrimaryButton, 
  SecondaryButton, 
  EmergencyButton 
} from './ui/AccessibleButton';

export interface Alert {
  id: string;
  level: AlertLevel;
  title: string;
  message: string;
  timestamp: Date;
  actions?: AlertAction[];
  autoClose?: boolean;
  persistent?: boolean;
  sound?: boolean;
  vibration?: boolean;
}

export interface AlertAction {
  label: string;
  action: () => void;
  style?: 'primary' | 'secondary' | 'emergency';
}

export interface AlertSystemProps {
  alerts: Alert[];
  onDismiss: (alertId: string) => void;
  emergencyMode?: boolean;
}

export function AlertSystem({ 
  alerts, 
  onDismiss, 
  emergencyMode = false 
}: AlertSystemProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [currentAlert, setCurrentAlert] = useState<Alert | null>(null);
  const [fadeAnim] = useState(new Animated.Value(0));

  // Mostrar la alerta más crítica
  useEffect(() => {
    if (alerts.length > 0) {
      // Ordenar por nivel de criticidad
      const sortedAlerts = [...alerts].sort((a, b) => {
        const levels = ['NORMAL', 'ADVISORY', 'WATCH', 'WARNING', 'EMERGENCY'];
        return levels.indexOf(b.level) - levels.indexOf(a.level);
      });
      
      const mostCritical = sortedAlerts[0];
      if (mostCritical && mostCritical.id !== currentAlert?.id) {
        setCurrentAlert(mostCritical);
        showAlert(mostCritical);
      }
    } else {
      setCurrentAlert(null);
    }
  }, [alerts]);

  const showAlert = (alert: Alert) => {
    // Efectos de notificación
    if (alert.vibration && Platform.OS !== 'web') {
      if (alert.level === 'EMERGENCY') {
        Vibration.vibrate([0, 500, 200, 500, 200, 500]);
      } else if (alert.level === 'WARNING') {
        Vibration.vibrate([0, 300, 100, 300]);
      } else {
        Vibration.vibrate(200);
      }
    }

    if (Platform.OS === 'ios') {
      const hapticType = alert.level === 'EMERGENCY' || alert.level === 'WARNING'
        ? Haptics.NotificationFeedbackType.Error
        : alert.level === 'WATCH'
        ? Haptics.NotificationFeedbackType.Warning
        : Haptics.NotificationFeedbackType.Success;
      
      Haptics.notificationAsync(hapticType);
    }

    // Animación de entrada
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 300,
      useNativeDriver: true,
    }).start();

    // Auto-close para alertas no críticas
    if (alert.autoClose && alert.level !== 'EMERGENCY' && alert.level !== 'WARNING') {
      setTimeout(() => {
        handleDismiss(alert.id);
      }, 5000);
    }
  };

  const handleDismiss = (alertId: string) => {
    Animated.timing(fadeAnim, {
      toValue: 0,
      duration: 200,
      useNativeDriver: true,
    }).start(() => {
      onDismiss(alertId);
      setCurrentAlert(null);
    });
  };

  const getAlertStyles = (level: AlertLevel) => {
    const alertColor = AlertLevels[level];
    const colorKey = `alert${alertColor.charAt(0).toUpperCase() + alertColor.slice(1)}` as keyof typeof colors;
    
    return {
      backgroundColor: colors[colorKey],
      borderColor: colors[colorKey],
    };
  };

  const getAlertIcon = (level: AlertLevel) => {
    const icons = {
      NORMAL: '✅',
      ADVISORY: '⚠️',
      WATCH: '🟠',
      WARNING: '🚨',
      EMERGENCY: '🆘',
    };
    return icons[level];
  };

  if (!currentAlert) return null;

  const alertStyles = getAlertStyles(currentAlert.level);
  const isEmergency = currentAlert.level === 'EMERGENCY';
  const isHighPriority = currentAlert.level === 'WARNING' || currentAlert.level === 'EMERGENCY';

  const styles = StyleSheet.create({
    overlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: isEmergency ? 'rgba(0, 0, 0, 0.9)' : 'rgba(0, 0, 0, 0.5)',
      justifyContent: isEmergency ? 'center' : 'flex-start',
      alignItems: 'center',
      zIndex: isEmergency ? ZIndex.emergency : ZIndex.alert,
      paddingTop: isEmergency ? 0 : 100,
      paddingHorizontal: Spacing.md,
    },

    alertContainer: {
      backgroundColor: colors.background,
      borderRadius: isEmergency ? BorderRadius['2xl'] : BorderRadius.lg,
      padding: isEmergency ? Spacing['2xl'] : Spacing.lg,
      width: '100%',
      maxWidth: isEmergency ? undefined : 400,
      borderLeftWidth: isEmergency ? 0 : 6,
      borderLeftColor: alertStyles.backgroundColor,
      ...Layout.card,
    },

    emergencyContainer: {
      backgroundColor: alertStyles.backgroundColor,
      borderWidth: 4,
      borderColor: colors.background,
    },

    header: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: Spacing.md,
    },

    icon: {
      fontSize: isEmergency ? 48 : 32,
      marginRight: Spacing.md,
    },

    titleContainer: {
      flex: 1,
    },

    content: {
      marginBottom: Spacing.lg,
    },

    actions: {
      gap: Spacing.sm,
    },

    emergencyActions: {
      gap: Spacing.md,
    },

    dismissButton: {
      position: 'absolute',
      top: Spacing.sm,
      right: Spacing.sm,
      width: 32,
      height: 32,
      borderRadius: 16,
      backgroundColor: colors.backgroundSecondary,
      justifyContent: 'center',
      alignItems: 'center',
    },
  });

  return (
    <Modal
      visible={true}
      transparent={true}
      animationType="none"
      statusBarTranslucent={true}
    >
      <Animated.View 
        style={[styles.overlay, { opacity: fadeAnim }]}
      >
        <View 
          style={[
            styles.alertContainer,
            isEmergency && styles.emergencyContainer,
          ]}
        >
          {/* Botón de cerrar (solo para alertas no críticas) */}
          {!isHighPriority && (
            <PrimaryButton
              style={styles.dismissButton}
              onPress={() => handleDismiss(currentAlert.id)}
              accessibilityLabel="Cerrar alerta"
            >
              ✕
            </PrimaryButton>
          )}

          {/* Header */}
          <View style={styles.header}>
            <BodyText style={styles.icon}>
              {getAlertIcon(currentAlert.level)}
            </BodyText>
            <View style={styles.titleContainer}>
              {isEmergency ? (
                <AlertTextLarge color={emergencyMode ? "inverse" : "error"}>
                  {currentAlert.title}
                </AlertTextLarge>
              ) : (
                <Heading3>{currentAlert.title}</Heading3>
              )}
            </View>
          </View>

          {/* Contenido */}
          <View style={styles.content}>
            {isEmergency ? (
              <AlertText color={emergencyMode ? "inverse" : "primary"}>
                {currentAlert.message}
              </AlertText>
            ) : (
              <BodyText>{currentAlert.message}</BodyText>
            )}
          </View>

          {/* Acciones */}
          <View style={isEmergency ? styles.emergencyActions : styles.actions}>
            {currentAlert.actions?.map((action, index) => {
              if (action.style === 'emergency') {
                return (
                  <EmergencyButton
                    key={index}
                    onPress={action.action}
                    fullWidth
                  >
                    {action.label}
                  </EmergencyButton>
                );
              } else if (action.style === 'primary') {
                return (
                  <PrimaryButton
                    key={index}
                    onPress={action.action}
                    fullWidth={isEmergency}
                    size={isEmergency ? 'large' : 'medium'}
                  >
                    {action.label}
                  </PrimaryButton>
                );
              } else {
                return (
                  <SecondaryButton
                    key={index}
                    onPress={action.action}
                    fullWidth={isEmergency}
                    size={isEmergency ? 'large' : 'medium'}
                  >
                    {action.label}
                  </SecondaryButton>
                );
              }
            })}

            {/* Botón de cerrar para alertas críticas */}
            {isHighPriority && (
              <SecondaryButton
                onPress={() => handleDismiss(currentAlert.id)}
                fullWidth={isEmergency}
                size={isEmergency ? 'large' : 'medium'}
              >
                Entendido
              </SecondaryButton>
            )}
          </View>
        </View>
      </Animated.View>
    </Modal>
  );
}

// Hook para gestionar alertas
export function useAlertSystem() {
  const [alerts, setAlerts] = useState<Alert[]>([]);

  const addAlert = (alert: Omit<Alert, 'id' | 'timestamp'>) => {
    const newAlert: Alert = {
      ...alert,
      id: Date.now().toString(),
      timestamp: new Date(),
    };
    setAlerts(prev => [...prev, newAlert]);
  };

  const dismissAlert = (alertId: string) => {
    setAlerts(prev => prev.filter(alert => alert.id !== alertId));
  };

  const clearAllAlerts = () => {
    setAlerts([]);
  };

  return {
    alerts,
    addAlert,
    dismissAlert,
    clearAllAlerts,
  };
}
