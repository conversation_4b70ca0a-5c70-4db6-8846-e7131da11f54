/**
 * Action Grid Component
 * Modern grid layout for quick actions
 * Inspired by travel app quick action layouts
 */

import React from 'react';
import {
  View,
  StyleSheet,
  Pressable,
  ViewStyle,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { AccessibleText } from './AccessibleText';
import { Colors } from '@/constants/Colors';
import { BorderRadius, Spacing } from '@/constants/Layout';
import { useColorScheme } from '@/hooks/useColorScheme';

export interface ActionItem {
  id: string;
  title: string;
  icon: React.ReactNode;
  onPress: () => void;
  gradientColors?: string[];
  size?: 'small' | 'medium' | 'large';
}

interface ActionGridProps {
  actions: ActionItem[];
  columns?: number;
  style?: ViewStyle;
}

export function ActionGrid({ actions, columns = 2, style }: ActionGridProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const renderActionItem = (action: ActionItem, index: number) => {
    const isLarge = action.size === 'large';
    const itemStyle = isLarge ? styles.actionItemLarge : styles.actionItem;
    
    return (
      <Pressable
        key={action.id}
        onPress={action.onPress}
        style={({ pressed }) => [
          itemStyle,
          { backgroundColor: colors.background },
          pressed && styles.pressed,
        ]}
        accessibilityRole="button"
        accessibilityLabel={action.title}
      >
        {action.gradientColors ? (
          <LinearGradient
            colors={action.gradientColors}
            style={StyleSheet.absoluteFillObject}
            start={{ x: 0, y: 0 }}
            end={{ x: 1, y: 1 }}
          />
        ) : null}
        
        <View style={styles.actionContent}>
          <View style={styles.iconContainer}>
            {action.icon}
          </View>
          <AccessibleText 
            variant="button" 
            style={[
              styles.actionText,
              { color: action.gradientColors ? 'white' : colors.text }
            ]}
          >
            {action.title}
          </AccessibleText>
        </View>
      </Pressable>
    );
  };

  return (
    <View style={[styles.container, style]}>
      <View style={[styles.grid, { gap: Spacing.sm }]}>
        {actions.map(renderActionItem)}
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.md,
  },

  grid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },

  actionItem: {
    width: '48%',
    aspectRatio: 1.2,
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.sm,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 6,
      },
      android: {
        elevation: 3,
      },
    }),
  },

  actionItemLarge: {
    width: '100%',
    aspectRatio: 2.5,
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.sm,
    overflow: 'hidden',
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 4 },
        shadowOpacity: 0.15,
        shadowRadius: 8,
      },
      android: {
        elevation: 4,
      },
    }),
  },

  actionContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.md,
    position: 'relative',
    zIndex: 1,
  },

  iconContainer: {
    marginBottom: Spacing.sm,
  },

  actionText: {
    textAlign: 'center',
    fontWeight: '600',
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },

  pressed: {
    opacity: 0.9,
    transform: [{ scale: 0.98 }],
  },
});