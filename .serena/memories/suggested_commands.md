# Suggested Commands & Development Workflow

## Core Development Commands

### Starting Development
```bash
npm start              # Start Expo development server
npm run android        # Start Android development
npm run ios           # Start iOS development  
npm run web           # Start web development
```

### Testing
```bash
npm test              # Run Jest tests in watch mode
npm run test:ci       # Run tests once (for CI)
```

### Project Structure Commands
```bash
# Navigate project structure
ls app/(tabs)/        # View tab screens
ls components/ui/     # View design system components
ls services/          # View service layer
ls constants/         # View configuration files
```

### Development Tools
```bash
# Expo CLI commands (when Expo dev server is running)
r                     # Reload app
m                     # Toggle menu
j                     # Open debugger
i                     # Open iOS simulator
a                     # Open Android emulator
w                     # Open web browser
```

### Git Workflow
```bash
git status            # Check file changes
git add .             # Stage all changes
git commit -m "message"  # Commit changes
git push              # Push to remote
```

### File Operations (macOS)
```bash
find . -name "*.tsx"  # Find TypeScript React files
grep -r "pattern"     # Search for code patterns
ls -la               # List files with details
cd path/to/dir       # Change directory
```

## Development Workflow

### When Starting a Task
1. **Read CLAUDE.md** - Review project guidelines
2. **Check current branch** - `git status`
3. **Start development server** - `npm start`
4. **Open relevant files** - Usually in `app/` or `components/`

### When Completing a Task
1. **Run tests** - `npm test` (ensure all pass)
2. **Check TypeScript** - Built into development server
3. **Test on device/simulator** - Verify UI/UX changes
4. **Commit changes** - Follow conventional commit format

### Code Quality Checks
- **TypeScript Compilation** - Automatic during development
- **Jest Testing** - `npm test` for component tests
- **Accessibility** - Manual testing with screen reader
- **Performance** - Monitor in Expo dev tools

## File Patterns & Locations

### Adding New Features
- **New Screen**: `app/(tabs)/new-screen.tsx`
- **New Component**: `components/NewComponent.tsx`
- **UI Component**: `components/ui/NewUIComponent.tsx`
- **Service**: `services/newService.ts`
- **Hook**: `hooks/useNewHook.ts`
- **Type**: `types/newTypes.ts`

### Common File Locations
- **App Entry**: `app/_layout.tsx`
- **Tab Navigation**: `app/(tabs)/_layout.tsx`
- **Home Screen**: `app/(tabs)/index.tsx`
- **Map Screen**: `app/(tabs)/explore.tsx`
- **Analysis Screen**: `app/(tabs)/precursors.tsx`
- **Colors/Theme**: `constants/Colors.ts`
- **Layout Constants**: `constants/Layout.ts`

## Environment Setup

### Required Environment Variables
```bash
# Create .env file
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_key
EXPO_PUBLIC_API_BASE_URL=your_api_url
```

### Platform-Specific Testing
- **iOS**: Xcode Simulator required
- **Android**: Android Studio/Emulator required  
- **Web**: Runs in browser automatically

## Common Development Tasks

### UI/UX Changes
1. Modify components in `components/ui/`
2. Update styles using `constants/Colors.ts` and `constants/Layout.ts`
3. Test accessibility with VoiceOver/TalkBack
4. Verify responsive design on different screen sizes

### Adding New API Integration
1. Add service method in `services/api.ts`
2. Create TypeScript types in `types/`
3. Add React Query hook in `hooks/useApi.ts`
4. Integrate in component with proper error handling

### Adding New Screen
1. Create file in `app/(tabs)/` for tab screen
2. Update `app/(tabs)/_layout.tsx` if needed
3. Add navigation types if using stack navigation
4. Test navigation flow and back button behavior