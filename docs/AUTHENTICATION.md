# 🔐 Sistema de Autenticación - VolkanApp Mobile

## Descripción General

El sistema de autenticación de VolkanApp Mobile está construido sobre **Supabase Auth** y proporciona una experiencia completa de registro, inicio de sesión y gestión de usuarios. El sistema está diseñado para ser seguro, escalable y fácil de usar, con soporte completo para planes familiares de emergencia.

## 🏗️ Arquitectura del Sistema

### Componentes Principales

```
┌─────────────────────────────────────────────────────────────┐
│                    APLICACIÓN MÓVIL                         │
├─────────────────────────────────────────────────────────────┤
│  🎨 UI Components                                           │
│  ├── LoginScreen (/app/(auth)/login.tsx)                   │
│  ├── RegisterScreen (/app/(auth)/register.tsx)             │
│  ├── UserProfile (/components/UserProfile.tsx)             │
│  └── UserHeader (/components/UserHeader.tsx)               │
├─────────────────────────────────────────────────────────────┤
│  🛡️ Protection Layer                                        │
│  ├── AuthGuard (/components/AuthGuard.tsx)                 │
│  ├── AuthNavigator (/components/AuthNavigator.tsx)         │
│  └── Route Protection Logic                                │
├─────────────────────────────────────────────────────────────┤
│  🔗 State Management                                        │
│  ├── AuthProvider (/providers/AuthProvider.tsx)            │
│  ├── useAuth Hook (/hooks/useAuth.ts)                      │
│  └── React Query Integration                               │
├─────────────────────────────────────────────────────────────┤
│  ⚙️ Services Layer                                          │
│  ├── AuthService (/services/auth.ts)                       │
│  ├── FamilyPlanService (/services/familyPlan.ts)           │
│  └── API Client (/services/api.ts)                         │
├─────────────────────────────────────────────────────────────┤
│  🗄️ Storage Layer                                           │
│  ├── AsyncStorage (Session Persistence)                    │
│  └── Supabase Client (/services/supabase.ts)               │
└─────────────────────────────────────────────────────────────┘
                              │
                              ▼
┌─────────────────────────────────────────────────────────────┐
│                      SUPABASE                               │
├─────────────────────────────────────────────────────────────┤
│  🔐 Authentication                                          │
│  ├── User Registration & Login                             │
│  ├── Session Management                                    │
│  ├── Token Refresh                                         │
│  └── Password Recovery                                     │
├─────────────────────────────────────────────────────────────┤
│  🗃️ Database                                                │
│  ├── auth.users (Supabase Auth Table)                      │
│  ├── user_profiles (Custom Profile Data)                   │
│  ├── family_plans (Family Emergency Plans)                 │
│  └── family_members (Family Plan Members)                  │
└─────────────────────────────────────────────────────────────┘
```

### Flujo de Datos

1. **Inicialización**: `AuthProvider` inicializa el estado de autenticación
2. **Autenticación**: `AuthService` maneja login/registro con Supabase
3. **Persistencia**: Sesiones se guardan en `AsyncStorage`
4. **Protección**: `AuthGuard` y `AuthNavigator` controlan acceso a rutas
5. **Integración**: APIs usan tokens de autenticación automáticamente

## 🔄 Flujos de Autenticación

### 1. Flujo de Registro

```mermaid
sequenceDiagram
    participant U as Usuario
    participant RS as RegisterScreen
    participant AS as AuthService
    participant SB as Supabase
    participant DB as Database

    U->>RS: Completa formulario
    RS->>AS: register(credentials)
    AS->>SB: signUp(email, password, metadata)
    SB->>DB: Crear usuario en auth.users
    SB-->>AS: Usuario creado + sesión
    AS->>DB: Crear perfil en user_profiles
    AS->>AS: Guardar en AsyncStorage
    AS-->>RS: AuthResponse(success: true)
    RS->>U: Redirigir a app principal
```

### 2. Flujo de Inicio de Sesión

```mermaid
sequenceDiagram
    participant U as Usuario
    participant LS as LoginScreen
    participant AS as AuthService
    participant SB as Supabase
    participant DB as Database

    U->>LS: Ingresa credenciales
    LS->>AS: login(email, password)
    AS->>SB: signInWithPassword()
    SB->>DB: Verificar credenciales
    SB-->>AS: Sesión válida
    AS->>DB: Obtener perfil de usuario
    AS->>AS: Guardar en AsyncStorage
    AS-->>LS: AuthResponse(success: true)
    LS->>U: Redirigir a app principal
```

### 3. Flujo de Inicialización

```mermaid
sequenceDiagram
    participant APP as App
    participant AP as AuthProvider
    participant AS as AuthService
    participant SB as Supabase
    participant ST as AsyncStorage

    APP->>AP: Inicializar
    AP->>AS: initialize()
    AS->>SB: getSession()
    alt Sesión válida
        SB-->>AS: Sesión activa
        AS->>SB: Obtener datos de usuario
        AS->>ST: Guardar sesión
        AS-->>AP: AuthState(authenticated: true)
    else Sin sesión
        SB-->>AS: No hay sesión
        AS-->>AP: AuthState(authenticated: false)
    end
    AP->>APP: Estado inicial listo
```

## 🛡️ Protección de Rutas

### Configuración de Rutas

El sistema clasifica las rutas en tres categorías:

```typescript
// Rutas que requieren autenticación
const PROTECTED_ROUTES = [
  '/(tabs)',
  '/family-plan',
  '/profile',
  '/settings',
];

// Rutas de autenticación (solo para usuarios no autenticados)
const AUTH_ROUTES = [
  '/(auth)',
  '/(auth)/login',
  '/(auth)/register',
];

// Rutas públicas (acceso libre)
const PUBLIC_ROUTES = [
  '/onboarding',
  '/about',
  '/help',
];
```

### Componentes de Protección

#### AuthGuard
Protege componentes individuales:

```typescript
<AuthGuard requireAuth={true}>
  <ProtectedComponent />
</AuthGuard>
```

#### AuthNavigator
Maneja redirecciones automáticas a nivel de aplicación:

```typescript
<AuthNavigator>
  <App />
</AuthNavigator>
```

### Lógica de Redirección

- **Usuario autenticado** en ruta de auth → Redirige a `/(tabs)`
- **Usuario no autenticado** en ruta protegida → Redirige a `/(auth)/login`
- **Rutas públicas** → Acceso libre para todos

## 🔗 Integración con Planes Familiares

### Autenticación en APIs

Todas las llamadas a la API de planes familiares incluyen automáticamente:

```typescript
// Headers automáticos
{
  'Authorization': 'Bearer <jwt_token>',
  'X-User-ID': '<user_id>',
  'Content-Type': 'application/json'
}
```

### Fallback a Device ID

Si el usuario no está autenticado, el sistema usa `Device ID` como fallback:

```typescript
// Interceptor de API
if (userId) {
  headers['X-User-ID'] = userId;
} else {
  const deviceId = await getDeviceId();
  headers['X-Device-ID'] = deviceId;
}
```

### Servicios Integrados

- **FamilyPlanService**: Gestión completa de planes familiares
- **Hooks especializados**: `useFamilyPlan`, `useFamilyPlanManager`
- **Sincronización**: Datos específicos por usuario autenticado

## 📊 Estructura de Datos

### Tabla: user_profiles

```sql
CREATE TABLE user_profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id),
  email TEXT NOT NULL,
  name TEXT,
  phone TEXT,
  avatar_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Interfaz: AuthUser

```typescript
interface AuthUser {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  phone?: string;
  created_at: string;
  updated_at: string;
}
```

### Estado de Autenticación

```typescript
interface AuthState {
  user: AuthUser | null;
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}
```

## 🔧 Configuración

### Variables de Entorno

```bash
# Supabase Configuration
EXPO_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=your-anon-key

# API Configuration
EXPO_PUBLIC_API_BASE_URL=http://localhost:3001
```

### Configuración de Supabase

```typescript
export const supabase = createClient(SUPABASE_URL, SUPABASE_ANON_KEY, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: false,
  },
});
```

## 🚨 Manejo de Errores

### Errores de Autenticación

El sistema maneja automáticamente:

- **401 Unauthorized**: Logout automático y redirección a login
- **Network errors**: Mensajes de error amigables
- **Validation errors**: Validación en tiempo real de formularios

### Mensajes de Error Localizados

```typescript
private getErrorMessage(error: AuthError): string {
  switch (error.message) {
    case 'Invalid login credentials':
      return 'Email o contraseña incorrectos';
    case 'User already registered':
      return 'Este email ya está registrado';
    // ... más casos
  }
}
```

## 🔄 Estados de Carga

### Estados Manejados

- **isInitializing**: Inicialización del sistema de auth
- **isLoading**: Operaciones de login/registro en progreso
- **isAuthenticated**: Estado de autenticación del usuario

### Componentes de Loading

```typescript
if (isInitializing) {
  return <LoadingScreen />;
}

if (requireAuth && !isAuthenticated) {
  return <AuthRequiredScreen />;
}
```

## 📱 Persistencia de Sesión

### AsyncStorage

Las sesiones se persisten automáticamente usando:

```typescript
const AUTH_STORAGE_KEYS = {
  SESSION: 'volcano_auth_session',
  USER: 'volcano_auth_user',
  DEVICE_ID: 'volcano_device_id',
};
```

### Recuperación de Sesión

Al inicializar la app:
1. Verificar sesión en Supabase
2. Obtener datos de usuario de la base de datos
3. Actualizar estado local
4. Guardar en AsyncStorage

## 🔒 Seguridad

### Mejores Prácticas Implementadas

- **JWT Tokens**: Tokens seguros con expiración automática
- **Refresh automático**: Renovación transparente de tokens
- **Validación de entrada**: Sanitización de datos de usuario
- **HTTPS**: Todas las comunicaciones encriptadas
- **Storage seguro**: AsyncStorage para datos sensibles

### Protección CSRF

Supabase maneja automáticamente la protección CSRF mediante:
- Tokens JWT firmados
- Headers de autenticación seguros
- Validación de origen

---

## 📚 Referencias

- [Documentación de Supabase Auth](https://supabase.com/docs/guides/auth)
- [React Native AsyncStorage](https://react-native-async-storage.github.io/async-storage/)
- [Expo Router Authentication](https://docs.expo.dev/router/reference/authentication/)

---

*Última actualización: 3 de enero de 2025*
