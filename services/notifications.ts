/**
 * 🌋 Volcano App Mobile - Servicio de Notificaciones con Supabase Realtime
 * Gestión de notificaciones locales y alertas en tiempo real usando Supabase
 */

import Constants from 'expo-constants';
import * as Device from 'expo-device';
import * as Haptics from 'expo-haptics';
import * as Notifications from 'expo-notifications';
import { Platform, Vibration } from 'react-native';
import {
    initializeSupabase,
    subscribeToRealtimeNotifications,
    unsubscribeFromRealtimeNotifications,
    type RealtimeNotification
} from './supabase';

// =====================================================
// TIPOS Y INTERFACES
// =====================================================

export interface NotificationData {
  type: 'volcano_alert' | 'zone_update' | 'system_status' | 'test';
  alertId?: string;
  alertLevel?: string;
  volcanoName?: string;
  volcanoLat?: number;
  volcanoLng?: number;
  zoneId?: string;
  zoneName?: string;
  zoneType?: string;
  action?: string;
  vibrationPattern?: number[];
  timestamp: string;
}

export interface NotificationPermissions {
  status: 'granted' | 'denied' | 'undetermined';
  canAskAgain: boolean;
  canScheduleExact: boolean;
}

// =====================================================
// CONFIGURACIÓN DE NOTIFICACIONES
// =====================================================

// Configurar el comportamiento de las notificaciones
Notifications.setNotificationHandler({
  handleNotification: async (notification) => {
    const data = notification.request.content.data as NotificationData;
    
    // Configurar según el tipo de notificación
    switch (data.type) {
      case 'volcano_alert':
        return {
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: true,
        };
      case 'zone_update':
        return {
          shouldShowAlert: true,
          shouldPlaySound: false,
          shouldSetBadge: false,
        };
      case 'system_status':
        return {
          shouldShowAlert: false,
          shouldPlaySound: false,
          shouldSetBadge: false,
        };
      case 'test':
        return {
          shouldShowAlert: true,
          shouldPlaySound: true,
          shouldSetBadge: false,
        };
      default:
        return {
          shouldShowAlert: true,
          shouldPlaySound: false,
          shouldSetBadge: false,
        };
    }
  },
});

// =====================================================
// SERVICIO DE NOTIFICACIONES
// =====================================================

class NotificationService {
  private isInitialized = false;
  private pushToken: string | null = null;
  private notificationListener: any = null;
  private responseListener: any = null;
  private realtimeChannel: any = null;

  /**
   * Inicializar el servicio de notificaciones
   */
  async initialize(): Promise<NotificationPermissions> {
    try {
      console.log('🔔 Initializing notification service...');

      // Inicializar Supabase primero
      const supabaseInitialized = await initializeSupabase();
      if (!supabaseInitialized) {
        console.warn('⚠️ Supabase initialization failed, continuing without realtime notifications');
      }

      // Verificar si es un dispositivo físico
      if (!Device.isDevice) {
        console.warn('⚠️ Push notifications only work on physical devices');
        return {
          status: 'denied',
          canAskAgain: false,
          canScheduleExact: false
        };
      }

      // Solicitar permisos
      const permissions = await this.requestPermissions();
      
      if (permissions.status === 'granted') {
        // Obtener token de push
        await this.registerForPushNotifications();
        
        // Configurar listeners
        this.setupNotificationListeners();

        // Configurar Supabase Realtime
        this.setupRealtimeSubscription();

        this.isInitialized = true;
        console.log('✅ Notification service initialized successfully');
      } else {
        console.warn('⚠️ Notification permissions not granted');
      }

      return permissions;

    } catch (error) {
      console.error('❌ Failed to initialize notification service:', error);
      throw error;
    }
  }

  /**
   * Solicitar permisos de notificaciones
   */
  async requestPermissions(): Promise<NotificationPermissions> {
    try {
      const { status: existingStatus } = await Notifications.getPermissionsAsync();
      let finalStatus = existingStatus;

      if (existingStatus !== 'granted') {
        const { status } = await Notifications.requestPermissionsAsync();
        finalStatus = status;
      }

      // En Android, también solicitar permisos de notificaciones exactas
      let canScheduleExact = true;
      if (Platform.OS === 'android') {
        try {
          const { status: exactStatus } = await Notifications.getPermissionsAsync();
          canScheduleExact = exactStatus === 'granted';
        } catch (error) {
          console.warn('Could not check exact notification permissions:', error);
        }
      }

      return {
        status: finalStatus as 'granted' | 'denied' | 'undetermined',
        canAskAgain: finalStatus !== 'denied',
        canScheduleExact
      };

    } catch (error) {
      console.error('Error requesting notification permissions:', error);
      return {
        status: 'denied',
        canAskAgain: false,
        canScheduleExact: false
      };
    }
  }

  /**
   * Registrar para notificaciones push
   */
  async registerForPushNotifications(): Promise<string | null> {
    try {
      const token = await Notifications.getExpoPushTokenAsync({
        projectId: Constants.expoConfig?.extra?.eas?.projectId,
      });

      this.pushToken = token.data;
      console.log('📱 Push token obtained:', this.pushToken);

      // Aquí podrías enviar el token al backend para registrar el dispositivo
      // await this.registerDeviceWithBackend(this.pushToken);

      return this.pushToken;

    } catch (error) {
      console.error('Error getting push token:', error);
      return null;
    }
  }

  /**
   * Configurar listeners de notificaciones
   */
  private setupNotificationListeners(): void {
    // Listener para notificaciones recibidas mientras la app está en primer plano
    this.notificationListener = Notifications.addNotificationReceivedListener(
      (notification) => {
        console.log('🔔 Notification received:', notification);
        this.handleNotificationReceived(notification);
      }
    );

    // Listener para cuando el usuario toca una notificación
    this.responseListener = Notifications.addNotificationResponseReceivedListener(
      (response) => {
        console.log('👆 Notification tapped:', response);
        this.handleNotificationResponse(response);
      }
    );
  }

  /**
   * Configurar suscripción a Supabase Realtime para notificaciones
   */
  private setupRealtimeSubscription(): void {
    try {
      console.log('🔔 Setting up Supabase Realtime notifications...');

      // Usar la función de suscripción del servicio de Supabase
      this.realtimeChannel = subscribeToRealtimeNotifications(
        (notification) => {
          this.handleRealtimeNotification(notification);
        },
        (error) => {
          console.error('❌ Realtime notification error:', error);
        }
      );

    } catch (error) {
      console.error('Error setting up Realtime subscription:', error);
    }
  }

  /**
   * Manejar notificación recibida via Supabase Realtime
   */
  private async handleRealtimeNotification(notification: RealtimeNotification): Promise<void> {
    try {
      if (!notification || !notification.data) {
        console.warn('Invalid realtime notification');
        return;
      }

      const notificationData = notification.data as NotificationData;

      // Mostrar notificación local
      await this.showLocalNotification(
        notification.title,
        notification.message,
        notificationData
      );

      // Activar efectos hápticos
      this.triggerHapticFeedback(notificationData);

      console.log('✅ Realtime notification processed successfully');

    } catch (error) {
      console.error('Error handling realtime notification:', error);
    }
  }

  /**
   * Manejar notificación recibida
   */
  private handleNotificationReceived(notification: Notifications.Notification): void {
    const data = notification.request.content.data as NotificationData;

    // Efectos hápticos y vibración según el tipo
    this.triggerHapticFeedback(data);
  }

  /**
   * Manejar respuesta a notificación (cuando el usuario la toca)
   */
  private handleNotificationResponse(response: Notifications.NotificationResponse): void {
    const data = response.notification.request.content.data as NotificationData;

    // Navegar a la pantalla apropiada según el tipo de notificación
    switch (data.type) {
      case 'volcano_alert':
        // Navegar a la pantalla de alertas
        console.log('Navigate to alerts screen');
        break;
      case 'zone_update':
        // Navegar a la pantalla del mapa
        console.log('Navigate to map screen');
        break;
      default:
        console.log('Default notification action');
    }
  }

  /**
   * Activar feedback háptico y vibración
   */
  private triggerHapticFeedback(data: NotificationData): void {
    try {
      // Vibración personalizada si está disponible
      if (data.vibrationPattern && data.vibrationPattern.length > 0) {
        if (Platform.OS === 'android') {
          Vibration.vibrate(data.vibrationPattern);
        } else {
          // En iOS, usar patrones predefinidos
          this.triggerIOSHaptics(data.type);
        }
      } else {
        // Vibración por defecto según el tipo
        this.triggerDefaultVibration(data.type);
      }

    } catch (error) {
      console.error('Error triggering haptic feedback:', error);
    }
  }

  /**
   * Activar hápticos en iOS
   */
  private triggerIOSHaptics(type: string): void {
    if (Platform.OS !== 'ios') return;

    switch (type) {
      case 'volcano_alert':
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
        break;
      case 'zone_update':
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
        break;
      case 'test':
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        break;
      default:
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    }
  }

  /**
   * Activar vibración por defecto
   */
  private triggerDefaultVibration(type: string): void {
    switch (type) {
      case 'volcano_alert':
        Vibration.vibrate([0, 1000, 500, 1000, 500, 1000]); // Patrón de emergencia
        break;
      case 'zone_update':
        Vibration.vibrate([0, 300, 100, 300]); // Patrón suave
        break;
      case 'test':
        Vibration.vibrate(200); // Vibración simple
        break;
      default:
        Vibration.vibrate(100);
    }
  }

  /**
   * Mostrar notificación local
   */
  async showLocalNotification(
    title: string,
    body: string,
    data?: NotificationData
  ): Promise<string> {
    try {
      const notificationId = await Notifications.scheduleNotificationAsync({
        content: {
          title,
          body,
          data: data || { type: 'test', timestamp: new Date().toISOString() },
          sound: true,
        },
        trigger: null, // Mostrar inmediatamente
      });

      return notificationId;

    } catch (error) {
      console.error('Error showing local notification:', error);
      throw error;
    }
  }

  /**
   * Limpiar listeners y suscripciones
   */
  cleanup(): void {
    if (this.notificationListener) {
      Notifications.removeNotificationSubscription(this.notificationListener);
    }
    if (this.responseListener) {
      Notifications.removeNotificationSubscription(this.responseListener);
    }
    if (this.realtimeChannel) {
      unsubscribeFromRealtimeNotifications(this.realtimeChannel);
      this.realtimeChannel = null;
    }
  }

  /**
   * Obtener token de push
   */
  getPushToken(): string | null {
    return this.pushToken;
  }

  /**
   * Verificar si está inicializado
   */
  isReady(): boolean {
    return this.isInitialized;
  }
}

// =====================================================
// EXPORTACIONES
// =====================================================

export const notificationService = new NotificationService();
export default notificationService;
