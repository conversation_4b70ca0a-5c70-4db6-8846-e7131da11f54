/**
 * 🌋 Volcano App Mobile - Authentication Provider
 * Proveedor de contexto para manejo de autenticación global
 */

import React, { createContext, useContext, useEffect, useState, ReactNode, useCallback, useMemo } from 'react';
import { authService, AuthState, AuthUser, LoginCredentials, RegisterCredentials, AuthResponse } from '@/services/auth';
import { supabase } from '@/services/supabase';
import { Session } from '@supabase/supabase-js';

// =====================================================
// TIPOS DEL CONTEXTO
// =====================================================

interface AuthContextType extends AuthState {
  // Métodos de autenticación
  login: (credentials: LoginCredentials) => Promise<AuthResponse>;
  register: (credentials: RegisterCredentials) => Promise<AuthResponse>;
  logout: () => Promise<boolean>;
  
  // Métodos de utilidad
  refreshUser: () => Promise<void>;
  updateProfile: (data: Partial<AuthUser>) => Promise<boolean>;
  
  // Estados adicionales
  isInitializing: boolean;
  error: string | null;
  clearError: () => void;
}

// =====================================================
// CONTEXTO
// =====================================================

const AuthContext = createContext<AuthContextType | undefined>(undefined);

// =====================================================
// PROVEEDOR
// =====================================================

interface AuthProviderProps {
  children: ReactNode;
}

export function AuthProvider({ children }: AuthProviderProps) {
  // Estados principales
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    session: null,
    isLoading: true,
    isAuthenticated: false,
  });
  
  // Estados adicionales
  const [isInitializing, setIsInitializing] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // =====================================================
  // INICIALIZACIÓN
  // =====================================================

  useEffect(() => {
    initializeAuth();
    setupAuthListener();
  }, []);

  const initializeAuth = async () => {
    try {
      console.log('🔐 Initializing AuthProvider...');
      setIsInitializing(true);
      
      const initialState = await authService.initialize();
      setAuthState(initialState);
      
      console.log('✅ AuthProvider initialized');
    } catch (error) {
      console.error('❌ Error initializing auth:', error);
      setError('Error al inicializar autenticación');
    } finally {
      setIsInitializing(false);
    }
  };

  const setupAuthListener = () => {
    // Escuchar cambios de autenticación de Supabase
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('🔐 Auth state changed:', event);
        
        try {
          if (event === 'SIGNED_IN' && session?.user) {
            // Usuario se autenticó
            const userData = await getUserData(session.user.id);
            setAuthState({
              user: userData,
              session: session,
              isLoading: false,
              isAuthenticated: true,
            });
          } else if (event === 'SIGNED_OUT') {
            // Usuario cerró sesión
            setAuthState({
              user: null,
              session: null,
              isLoading: false,
              isAuthenticated: false,
            });
          } else if (event === 'TOKEN_REFRESHED' && session) {
            // Token renovado
            setAuthState(prev => ({
              ...prev,
              session: session,
            }));
          }
        } catch (error) {
          console.error('❌ Error handling auth state change:', error);
          setError('Error al procesar cambio de autenticación');
        }
      }
    );

    // Cleanup
    return () => {
      subscription.unsubscribe();
    };
  };

  // =====================================================
  // MÉTODOS DE AUTENTICACIÓN
  // =====================================================

  const login = useCallback(async (credentials: LoginCredentials): Promise<AuthResponse> => {
    try {
      setError(null);
      setAuthState(prev => ({ ...prev, isLoading: true }));

      const response = await authService.login(credentials);

      if (response.success && response.user && response.session) {
        setAuthState({
          user: response.user,
          session: response.session,
          isLoading: false,
          isAuthenticated: true,
        });
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }));
        if (response.error) {
          setError(response.error);
        }
      }

      return response;
    } catch (error) {
      console.error('❌ Login error in provider:', error);
      setAuthState(prev => ({ ...prev, isLoading: false }));
      setError('Error de red durante el login');

      return {
        success: false,
        error: 'Error de red durante el login',
      };
    }
  }, []);

  const register = useCallback(async (credentials: RegisterCredentials): Promise<AuthResponse> => {
    try {
      setError(null);
      setAuthState(prev => ({ ...prev, isLoading: true }));

      const response = await authService.register(credentials);

      if (response.success && response.user && response.session) {
        setAuthState({
          user: response.user,
          session: response.session,
          isLoading: false,
          isAuthenticated: true,
        });
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }));
        if (response.error) {
          setError(response.error);
        }
      }

      return response;
    } catch (error) {
      console.error('❌ Register error in provider:', error);
      setAuthState(prev => ({ ...prev, isLoading: false }));
      setError('Error de red durante el registro');

      return {
        success: false,
        error: 'Error de red durante el registro',
      };
    }
  }, []);

  const logout = useCallback(async (): Promise<boolean> => {
    try {
      setError(null);
      setAuthState(prev => ({ ...prev, isLoading: true }));

      const success = await authService.logout();

      if (success) {
        setAuthState({
          user: null,
          session: null,
          isLoading: false,
          isAuthenticated: false,
        });
      } else {
        setAuthState(prev => ({ ...prev, isLoading: false }));
        setError('Error al cerrar sesión');
      }

      return success;
    } catch (error) {
      console.error('❌ Logout error in provider:', error);
      setAuthState(prev => ({ ...prev, isLoading: false }));
      setError('Error de red durante el logout');
      return false;
    }
  }, []);

  // =====================================================
  // MÉTODOS DE UTILIDAD
  // =====================================================

  const refreshUser = useCallback(async (): Promise<void> => {
    try {
      if (!authState.user?.id) return;

      const userData = await getUserData(authState.user.id);
      setAuthState(prev => ({
        ...prev,
        user: userData,
      }));
    } catch (error) {
      console.error('❌ Error refreshing user:', error);
      setError('Error al actualizar datos del usuario');
    }
  }, [authState.user?.id]);

  const updateProfile = useCallback(async (data: Partial<AuthUser>): Promise<boolean> => {
    try {
      if (!authState.user?.id) return false;

      const { error } = await supabase
        .from('user_profiles')
        .update({
          ...data,
          updated_at: new Date().toISOString(),
        })
        .eq('id', authState.user.id);

      if (error) {
        console.error('❌ Error updating profile:', error);
        setError('Error al actualizar perfil');
        return false;
      }

      // Actualizar estado local
      setAuthState(prev => ({
        ...prev,
        user: prev.user ? { ...prev.user, ...data } : null,
      }));

      return true;
    } catch (error) {
      console.error('❌ Error updating profile:', error);
      setError('Error de red al actualizar perfil');
      return false;
    }
  }, [authState.user?.id]);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // =====================================================
  // MÉTODOS AUXILIARES
  // =====================================================

  const getUserData = async (userId: string): Promise<AuthUser> => {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error || !data) {
        throw new Error('User profile not found');
      }

      return {
        id: data.id,
        email: data.email,
        name: data.name,
        avatar_url: data.avatar_url,
        phone: data.phone,
        created_at: data.created_at,
        updated_at: data.updated_at,
      };
    } catch (error) {
      console.error('❌ Error getting user data:', error);
      throw error;
    }
  };

  // =====================================================
  // VALOR DEL CONTEXTO MEMOIZADO
  // =====================================================

  const contextValue: AuthContextType = useMemo(() => ({
    // Estado de autenticación
    ...authState,

    // Métodos de autenticación
    login,
    register,
    logout,

    // Métodos de utilidad
    refreshUser,
    updateProfile,

    // Estados adicionales
    isInitializing,
    error,
    clearError,
  }), [
    authState,
    login,
    register,
    logout,
    refreshUser,
    updateProfile,
    isInitializing,
    error,
    clearError,
  ]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
}

// =====================================================
// HOOK PERSONALIZADO
// =====================================================

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  return context;
}

// =====================================================
// EXPORTACIONES ADICIONALES
// =====================================================

export { AuthContext };
export type { AuthContextType };
