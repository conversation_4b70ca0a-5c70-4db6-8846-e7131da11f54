/**
 * 🌋 Volcano App Mobile - Hook de Notificaciones
 * Hook personalizado para gestionar notificaciones push y alertas en tiempo real
 */

import { NotificationPermissions } from '@/services/notifications-simple';
import { useWebSocket } from '@/services/websocket';
import { useCallback, useEffect, useState } from 'react';
import { AppState, AppStateStatus } from 'react-native';

// Importar el servicio de notificaciones
import simpleNotificationService from '@/services/notifications-simple';
const notificationService = simpleNotificationService;

// =====================================================
// TIPOS Y INTERFACES
// =====================================================

export interface NotificationState {
  isInitialized: boolean;
  permissions: NotificationPermissions | null;
  pushToken: string | null;
  lastNotification: any | null;
  error: string | null;
}

export interface UseNotificationsReturn {
  state: NotificationState;
  requestPermissions: () => Promise<NotificationPermissions>;
  showTestNotification: () => Promise<void>;
  refreshPermissions: () => Promise<void>;
  isReady: boolean;
}

// =====================================================
// HOOK DE NOTIFICACIONES
// =====================================================

export function useNotifications(): UseNotificationsReturn {
  const [state, setState] = useState<NotificationState>({
    isInitialized: false,
    permissions: null,
    pushToken: null,
    lastNotification: null,
    error: null,
  });

  const { isConnected, lastEvent } = useWebSocket();

  // =====================================================
  // FUNCIONES AUXILIARES
  // =====================================================

  const updateState = useCallback((updates: Partial<NotificationState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  const requestPermissions = useCallback(async (): Promise<NotificationPermissions> => {
    try {
      updateState({ error: null });
      
      const permissions = await notificationService.requestPermissions();
      updateState({ permissions });
      
      if (permissions.status === 'granted' && !state.isInitialized) {
        await initializeNotifications();
      }
      
      return permissions;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to request permissions';
      updateState({ error: errorMessage });
      throw error;
    }
  }, [state.isInitialized]);

  const initializeNotifications = useCallback(async () => {
    try {
      updateState({ error: null });
      
      const permissions = await notificationService.initialize();
      const pushToken = notificationService.getPushToken();
      
      updateState({
        isInitialized: true,
        permissions,
        pushToken,
      });

      console.log('✅ Notifications initialized successfully');
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to initialize notifications';
      updateState({ error: errorMessage });
      console.error('❌ Failed to initialize notifications:', error);
    }
  }, []);

  const showTestNotification = useCallback(async () => {
    try {
      if (!notificationService.isReady()) {
        throw new Error('Notification service not ready');
      }

      await notificationService.showLocalNotification(
        '🧪 Notificación de Prueba',
        'Esta es una notificación de prueba del sistema Volcano App',
        {
          type: 'test',
          timestamp: new Date().toISOString()
        }
      );

      console.log('✅ Test notification sent');
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to show test notification';
      updateState({ error: errorMessage });
      throw error;
    }
  }, []);

  const refreshPermissions = useCallback(async () => {
    try {
      const permissions = await notificationService.requestPermissions();
      updateState({ permissions });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to refresh permissions';
      updateState({ error: errorMessage });
    }
  }, []);

  // =====================================================
  // EFECTOS
  // =====================================================

  // Inicialización automática
  useEffect(() => {
    let mounted = true;

    const initialize = async () => {
      try {
        // Verificar permisos existentes
        const permissions = await notificationService.requestPermissions();
        
        if (!mounted) return;
        
        if (permissions.status === 'granted') {
          await initializeNotifications();
        } else {
          updateState({ permissions });
        }
      } catch (error) {
        if (!mounted) return;
        
        const errorMessage = error instanceof Error ? error.message : 'Initialization failed';
        updateState({ error: errorMessage });
      }
    };

    initialize();

    return () => {
      mounted = false;
    };
  }, []);

  // Manejar cambios de estado de la app
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'active' && state.isInitialized) {
        // Refrescar permisos cuando la app vuelve al primer plano
        refreshPermissions();
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    
    return () => {
      subscription?.remove();
    };
  }, [state.isInitialized, refreshPermissions]);

  // Manejar eventos de WebSocket para notificaciones en tiempo real
  useEffect(() => {
    if (!isConnected || !lastEvent) return;

    const handleWebSocketEvent = async () => {
      try {
        switch (lastEvent.type) {
          case 'alert:created':
          case 'alert:updated':
            if (lastEvent.data && notificationService.isReady()) {
              const alert = lastEvent.data;
              await notificationService.showLocalNotification(
                `🌋 Alerta Volcánica: ${alert.alert_level}`,
                alert.message || `${alert.volcano_name} - ${alert.title}`,
                {
                  type: 'volcano_alert',
                  alertId: alert.id,
                  alertLevel: alert.alert_level,
                  volcanoName: alert.volcano_name,
                  volcanoLat: alert.volcano_lat,
                  volcanoLng: alert.volcano_lng,
                  timestamp: new Date().toISOString()
                }
              );
              
              updateState({ lastNotification: alert });
            }
            break;

          case 'zone:updated':
            if (lastEvent.data && notificationService.isReady()) {
              const zone = lastEvent.data;
              await notificationService.showLocalNotification(
                '🗺️ Zona de Seguridad Actualizada',
                `La zona "${zone.name}" ha sido actualizada`,
                {
                  type: 'zone_update',
                  zoneId: zone.id,
                  zoneName: zone.name,
                  zoneType: zone.zone_type,
                  action: 'updated',
                  timestamp: new Date().toISOString()
                }
              );
              
              updateState({ lastNotification: zone });
            }
            break;
        }
      } catch (error) {
        console.error('Error handling WebSocket notification:', error);
      }
    };

    handleWebSocketEvent();
  }, [isConnected, lastEvent]);

  // Cleanup al desmontar
  useEffect(() => {
    return () => {
      notificationService.cleanup();
    };
  }, []);

  // =====================================================
  // VALOR DE RETORNO
  // =====================================================

  return {
    state,
    requestPermissions,
    showTestNotification,
    refreshPermissions,
    isReady: state.isInitialized && state.permissions?.status === 'granted',
  };
}

export default useNotifications;
