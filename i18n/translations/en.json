{"app": {"name": "VolkanApp", "slogan": "Real-time volcanic monitoring"}, "navigation": {"home": "Home", "explore": "Explore", "precursors": "Precursors"}, "common": {"loading": "Loading...", "error": "Error", "retry": "Retry", "cancel": "Cancel", "ok": "OK", "yes": "Yes", "no": "No", "save": "Save", "edit": "Edit", "delete": "Delete", "close": "Close", "next": "Next", "previous": "Previous", "continue": "Continue", "finish": "Finish", "lessThanHour": "Less than 1 hour ago", "hoursAgo": "{{count}} hours ago", "daysAgo": "{{count}} days ago"}, "alerts": {"levels": {"green": "Green", "yellow": "Yellow", "orange": "Orange", "red": "Red", "purple": "Purple"}, "status": {"normal": "Normal", "advisory": "Advisory", "watch": "Watch", "warning": "Warning", "emergency": "Emergency"}}, "home": {"title": "Volcano App", "subtitle": "Real-time volcanic monitoring", "lastUpdate": "Last update: {{time}}", "currentStatus": "Current Status", "quickActions": "Quick Actions", "recentUpdates": "Recent Updates", "emergency": "EMERGENCY", "interactiveMap": "Interactive Map", "precursors": "Precursors", "safetyGuides": "Safety Guides", "officialNews": "Official News", "footer": {"data": "Official data from SERNAGEOMIN • ONEMI", "pullToRefresh": "Pull down to refresh"}}, "map": {"title": "Volcanic Safety Map", "subtitle": "Villarrica Volcano • Real-time", "yourLocation": "Your location", "distanceToVolcano": "Distance to volcano", "evacuation": "Evacuation", "tips": "Tips"}, "precursors": {"title": "Precursor Analysis", "subtitle": "Early detection • Villarrica Volcano", "dataExamples": "Data Examples", "currentDataset": "Current Dataset", "type": "Type", "unit": "Unit", "dataPoints": "Data Points", "volcano": "Volcano", "howItWorks": "How It Works", "explanation": "This system analyzes the {{bold}}rate of change{{/bold}} (first derivative) and {{bold}}acceleration{{/bold}} (second derivative) of volcanic precursor data.", "greenLevel": "Green (≤ 1): Stable activity", "yellowLevel": "Yellow (1-5): Caution, accelerating activity", "redLevel": "Red (> 5): Alert, dangerous acceleration"}, "volcano": {"name": "Villarrica Volcano", "location": "Pucón, Araucanía Region", "elevation": "2,847 meters", "status": {"loading": "Loading volcano status...", "error": "Error loading volcano status", "updated": "Updated: {{time}}"}}, "emergency": {"title": "Volcanic Emergency", "question": "Do you need immediate help?", "call133": "Call 133 (Emergency)", "calling": "Calling", "connecting": "Connecting to emergency services...", "evacuationRoutes": "View Evacuation Routes", "navigating": "Navigating", "openingRoutes": "Opening evacuation routes map..."}, "evacuation": {"title": "Evacuation Routes", "description": "In case of volcanic emergency:", "primaryRoute": "Primary Route: To Temuco via Route 199", "alternativeRoute": "Alternative Route: To Villarrica via Route 199", "shelters": "Shelters: Pucón Center, Hospital, Schools", "fuelTip": "Always keep your fuel tank full.", "understood": "Understood"}, "safety": {"title": "Safety Tips", "locationMonitored": "Your current location is being monitored", "stayAway": "Stay away from the volcano", "emergencyKit": "Have an emergency kit ready", "knowRoutes": "Know the evacuation routes", "stayInformed": "Stay informed about official alerts", "distanceToVolcano": "Distance to volcano: {{distance}} km"}, "connection": {"title": "Connection Status", "apiBackend": "API Backend", "websocket": "WebSocket", "connected": "Connected", "disconnected": "Disconnected", "lastUpdate": "Last update: {{time}}"}, "api": {"testPanel": "API Test Panel", "testIntegration": "Test backend integration", "connectionStatus": "Connection Status", "apiHealth": "API Health", "websocket": "WebSocket", "loadedData": "Loaded data", "testResults": "Test Results", "pressButtons": "Press buttons to test the API"}, "language": {"title": "Language", "select": "Select language", "current": "Current language", "change": "Change language", "spanish": "Español", "english": "English", "haitianCreole": "<PERSON><PERSON><PERSON><PERSON>"}, "accessibility": {"emergency": "Emergency button - Call for immediate help", "emergencyHint": "Activates emergency protocols and calls emergency services", "evacuation": "View evacuation routes", "tips": "View safety tips"}}