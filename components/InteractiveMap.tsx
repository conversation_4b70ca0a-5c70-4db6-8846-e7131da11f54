/**
 * InteractiveMap Component
 * Mapa interactivo con Leaflet para mostrar el Volcán Villarrica,
 * zonas de seguridad, ubicación del usuario y rutas de evacuación
 */

import { Colors } from '@/constants/Colors';
import { BorderRadius, Layout, Spacing } from '@/constants/Layout';
import { useLocationManager, useMobileConfig, useSafetyZones } from '@/hooks/useApi';
import { useColorScheme } from '@/hooks/useColorScheme';
import * as Location from 'expo-location';
import React, { useEffect, useRef, useState } from 'react';
import {
    Alert,
    Dimensions,
    Platform,
    StyleSheet,
    View,
} from 'react-native';
import { WebView } from 'react-native-webview';
import { PrimaryButton, SecondaryButton } from './ui/AccessibleButton';
import { AccessibleText } from './ui/AccessibleText';
import {
    LocationIcon,
    RefreshIcon,
    VolcanoIcon
} from './ui/ModernIcon';

const { width, height } = Dimensions.get('window');

// Coordenadas del Volcán Villarrica
const VOLCANO_COORDS = {
  lat: -39.420000,
  lng: -71.939167,
};

// Zonas de seguridad predefinidas (ejemplos)
const SAFETY_ZONES = [
  {
    id: 'pucon-centro',
    name: 'Centro de Pucón',
    coords: [
      [-39.2700, -71.9500],
      [-39.2800, -71.9500],
      [-39.2800, -71.9400],
      [-39.2700, -71.9400],
    ],
    type: 'safe',
  },
  {
    id: 'hospital-pucon',
    name: 'Hospital de Pucón',
    coords: [
      [-39.2750, -71.9450],
      [-39.2760, -71.9450],
      [-39.2760, -71.9440],
      [-39.2750, -71.9440],
    ],
    type: 'emergency',
  },
];

export interface InteractiveMapProps {
  showUserLocation?: boolean;
  showSafetyZones?: boolean;
  onLocationUpdate?: (location: { lat: number; lng: number }) => void;
}

export function InteractiveMap({
  showUserLocation = true,
  showSafetyZones = true,
  onLocationUpdate,
}: InteractiveMapProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const webViewRef = useRef<WebView>(null);

  const [userLocation, setUserLocation] = useState<{lat: number; lng: number} | null>(null);
  const [locationPermission, setLocationPermission] = useState<boolean>(false);
  const [isMapReady, setIsMapReady] = useState(false);

  // API hooks
  const { data: zones, isLoading: zonesLoading } = useSafetyZones();
  const { data: config } = useMobileConfig();
  const { reportLocation } = useLocationManager();

  // Solicitar permisos de ubicación
  useEffect(() => {
    requestLocationPermission();
  }, []);

  const requestLocationPermission = async () => {
    try {
      const { status } = await Location.requestForegroundPermissionsAsync();
      if (status === 'granted') {
        setLocationPermission(true);
        getCurrentLocation();
      } else {
        Alert.alert(
          'Permisos de Ubicación',
          'Para mostrar tu ubicación en el mapa, necesitamos acceso a tu GPS.',
          [
            { text: 'Cancelar', style: 'cancel' },
            { text: 'Configurar', onPress: () => Location.requestForegroundPermissionsAsync() },
          ]
        );
      }
    } catch (error) {
      console.error('Error requesting location permission:', error);
    }
  };

  const getCurrentLocation = async () => {
    try {
      const location = await Location.getCurrentPositionAsync({
        accuracy: Location.Accuracy.High,
      });

      const coords = {
        lat: location.coords.latitude,
        lng: location.coords.longitude,
      };

      setUserLocation(coords);
      onLocationUpdate?.(coords);

      // Reportar ubicación a la API
      try {
        await reportLocation({
          latitude: coords.lat,
          longitude: coords.lng,
          accuracy: location.coords.accuracy || undefined,
          timestamp: new Date().toISOString(),
          app_version: config?.mobile_app_version,
          device_type: Platform.OS,
        });
      } catch (apiError) {
        console.warn('Failed to report location to API:', apiError);
      }

      // Actualizar ubicación en el mapa
      if (isMapReady && webViewRef.current) {
        webViewRef.current.postMessage(JSON.stringify({
          type: 'updateUserLocation',
          data: coords,
        }));
      }
    } catch (error) {
      console.error('Error getting location:', error);
      Alert.alert('Error', 'No se pudo obtener tu ubicación actual.');
    }
  };

  // HTML del mapa con Leaflet
  const mapHTML = `
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
        <title>Volcano Map</title>
        <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css" />
        <link rel="stylesheet" href="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.css" />
        <style>
            body { margin: 0; padding: 0; }
            #map { height: 100vh; width: 100vw; }
            .volcano-icon { 
                background: #EF4444; 
                border-radius: 50%; 
                border: 2px solid #fff;
                box-shadow: 0 2px 4px rgba(0,0,0,0.3);
            }
            .user-location { 
                background: #3B82F6; 
                border-radius: 50%; 
                border: 3px solid #fff;
                box-shadow: 0 2px 8px rgba(59,130,246,0.5);
            }
            .safety-zone { 
                fill-opacity: 0.3; 
                stroke-width: 2; 
            }
            .safe-zone { 
                fill: #22C55E; 
                stroke: #16A34A; 
            }
            .emergency-zone { 
                fill: #F59E0B; 
                stroke: #D97706; 
            }
            .danger-zone { 
                fill: #EF4444; 
                stroke: #DC2626; 
            }
        </style>
    </head>
    <body>
        <div id="map"></div>
        
        <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"></script>
        <script src="https://unpkg.com/leaflet-draw@1.0.4/dist/leaflet.draw.js"></script>
        
        <script>
            // Inicializar mapa
            const map = L.map('map').setView([${VOLCANO_COORDS.lat}, ${VOLCANO_COORDS.lng}], 10);
            
            // Capa base del mapa
            L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                attribution: '© OpenStreetMap contributors',
                maxZoom: 18,
            }).addTo(map);
            
            // Marcador del volcán
            const volcanoIcon = L.divIcon({
                className: 'volcano-icon',
                html: '🌋',
                iconSize: [30, 30],
                iconAnchor: [15, 15],
            });
            
            const volcanoMarker = L.marker([${VOLCANO_COORDS.lat}, ${VOLCANO_COORDS.lng}], {
                icon: volcanoIcon
            }).addTo(map);
            
            volcanoMarker.bindPopup('<b>Volcán Villarrica</b><br>Estado: Vigilancia<br>Última actividad: Hace 2 horas');
            
            // Marcador de ubicación del usuario
            let userMarker = null;
            const userIcon = L.divIcon({
                className: 'user-location',
                html: '📍',
                iconSize: [20, 20],
                iconAnchor: [10, 10],
            });
            
            // Zonas de seguridad desde la API
            const safetyZones = ${JSON.stringify(zones || SAFETY_ZONES)};

            safetyZones.forEach(zone => {
                // Convertir coordenadas de la API al formato de Leaflet
                let coords;
                if (zone.geometry && zone.geometry.coordinates) {
                    // Formato GeoJSON de la API
                    coords = zone.geometry.coordinates[0].map(coord => [coord[1], coord[0]]);
                } else if (zone.coords) {
                    // Formato legacy
                    coords = zone.coords;
                } else {
                    return; // Skip si no hay coordenadas válidas
                }

                const zoneTypeClass = zone.zone_type ? zone.zone_type.toLowerCase() : zone.type;
                const polygon = L.polygon(coords, {
                    className: 'safety-zone ' + zoneTypeClass + '-zone'
                }).addTo(map);

                const zoneTypeName = {
                    'safe': 'Zona Segura',
                    'emergency': 'Centro de Emergencia',
                    'danger': 'Zona de Peligro',
                    'evacuation': 'Ruta de Evacuación',
                    'restricted': 'Zona Restringida'
                };

                const popupContent = '<b>' + zone.name + '</b><br>' +
                    'Tipo: ' + (zoneTypeName[zoneTypeClass] || zoneTypeClass) +
                    (zone.description ? '<br>' + zone.description : '') +
                    (zone.capacity ? '<br>Capacidad: ' + zone.capacity + ' personas' : '');

                polygon.bindPopup(popupContent);
            });
            
            // Nota: Funcionalidad de dibujo removida para usuarios móviles
            // Solo se muestran zonas oficiales creadas desde el backoffice
            
            // Función para actualizar ubicación del usuario
            function updateUserLocation(coords) {
                if (userMarker) {
                    map.removeLayer(userMarker);
                }
                
                userMarker = L.marker([coords.lat, coords.lng], {
                    icon: userIcon
                }).addTo(map);
                
                userMarker.bindPopup('<b>Tu ubicación</b><br>Lat: ' + coords.lat.toFixed(6) + '<br>Lng: ' + coords.lng.toFixed(6));
                
                // Calcular distancia al volcán
                const distance = map.distance([coords.lat, coords.lng], [${VOLCANO_COORDS.lat}, ${VOLCANO_COORDS.lng}]);
                const distanceKm = (distance / 1000).toFixed(2);
                
                window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'locationUpdate',
                    data: {
                        coords: coords,
                        distanceToVolcano: distanceKm
                    }
                }));
            }
            
            // Escuchar mensajes desde React Native
            window.addEventListener('message', function(event) {
                const message = JSON.parse(event.data);
                
                switch(message.type) {
                    case 'updateUserLocation':
                        updateUserLocation(message.data);
                        break;
                    case 'centerOnUser':
                        if (userMarker) {
                            map.setView(userMarker.getLatLng(), 14);
                        }
                        break;
                    case 'centerOnVolcano':
                        map.setView([${VOLCANO_COORDS.lat}, ${VOLCANO_COORDS.lng}], 12);
                        break;
                }
            });
            
            // Notificar que el mapa está listo
            window.ReactNativeWebView.postMessage(JSON.stringify({
                type: 'mapReady'
            }));
        </script>
    </body>
    </html>
  `;

  const handleWebViewMessage = (event: any) => {
    try {
      const message = JSON.parse(event.nativeEvent.data);
      
      switch (message.type) {
        case 'mapReady':
          setIsMapReady(true);
          if (userLocation) {
            webViewRef.current?.postMessage(JSON.stringify({
              type: 'updateUserLocation',
              data: userLocation,
            }));
          }
          break;
          
        case 'locationUpdate':
          const { coords, distanceToVolcano } = message.data;
          Alert.alert(
            'Ubicación Actualizada',
            `Distancia al volcán: ${distanceToVolcano} km`,
            [{ text: 'OK' }]
          );
          break;
          
        // Funcionalidad de creación de zonas removida para usuarios móviles
      }
    } catch (error) {
      console.error('Error parsing WebView message:', error);
    }
  };

  const centerOnUser = () => {
    if (webViewRef.current) {
      webViewRef.current.postMessage(JSON.stringify({
        type: 'centerOnUser'
      }));
    }
  };

  const centerOnVolcano = () => {
    if (webViewRef.current) {
      webViewRef.current.postMessage(JSON.stringify({
        type: 'centerOnVolcano'
      }));
    }
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },
    
    mapContainer: {
      flex: 1,
      borderRadius: BorderRadius.lg,
      overflow: 'hidden',
      margin: Spacing.sm,
    },
    
    controls: {
      position: 'absolute',
      top: 50,
      right: Spacing.md,
      gap: Spacing.sm,
    },
    
    controlButton: {
      backgroundColor: colors.background,
      borderRadius: BorderRadius.md,
      padding: Spacing.sm,
      ...Layout.card,
    },
    
    locationInfo: {
      position: 'absolute',
      bottom: 100,
      left: Spacing.md,
      right: Spacing.md,
      backgroundColor: colors.background,
      padding: Spacing.md,
      borderRadius: BorderRadius.lg,
      ...Layout.card,
    },
  });

  return (
    <View style={styles.container}>
      <View style={styles.mapContainer}>
        <WebView
          ref={webViewRef}
          source={{ html: mapHTML }}
          style={{ flex: 1 }}
          onMessage={handleWebViewMessage}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          startInLoadingState={true}
          scalesPageToFit={true}
          scrollEnabled={true}
          bounces={false}
        />
      </View>
      
      {/* Controles del mapa */}
      <View style={styles.controls}>
        <PrimaryButton
          style={styles.controlButton}
          onPress={centerOnVolcano}
          accessibilityLabel="Centrar en volcán"
          icon={<VolcanoIcon size={20} color="white" strokeWidth={2.5} />}
        >
        </PrimaryButton>

        {locationPermission && (
          <SecondaryButton
            style={styles.controlButton}
            onPress={centerOnUser}
            accessibilityLabel="Centrar en mi ubicación"
            icon={<LocationIcon size={20} strokeWidth={2.5} />}
          >
          </SecondaryButton>
        )}

        <SecondaryButton
          style={styles.controlButton}
          onPress={getCurrentLocation}
          accessibilityLabel="Actualizar ubicación"
          icon={<RefreshIcon size={20} strokeWidth={2.5} />}
        >
        </SecondaryButton>
      </View>
      
      {/* Información de ubicación */}
      {userLocation && (
        <View style={styles.locationInfo}>
          <AccessibleText variant="bodySmall" color="secondary">
            Tu ubicación: {userLocation.lat.toFixed(6)}, {userLocation.lng.toFixed(6)}
          </AccessibleText>
        </View>
      )}
    </View>
  );
}
