# Code Style & Conventions

## TypeScript Configuration
- **Strict Mode**: Enabled for maximum type safety
- **Path Aliases**: `@/*` resolves to project root
- **Import Order**: External packages → Internal modules → Relative imports
- **File Extensions**: `.tsx` for React components, `.ts` for utilities

## Naming Conventions

### Files and Directories
- **Components**: PascalCase - `ModernCard.tsx`, `VolcanoStatus.tsx`
- **Screens**: PascalCase - `index.tsx`, `explore.tsx`, `precursors.tsx`  
- **Services**: camelCase - `api.ts`, `websocket.ts`, `notifications.ts`
- **Hooks**: camelCase with 'use' prefix - `useApi.ts`, `useColorScheme.ts`
- **Types**: camelCase - `volcano.ts`, `precursor.ts`
- **Constants**: PascalCase - `Colors.ts`, `Layout.ts`

### Variables and Functions
- **Variables**: camelCase - `volcanoData`, `currentAlert`, `isLoading`
- **Functions**: camelCase - `handleEmergencyAction`, `formatAlertTime`
- **Constants**: UPPER_SNAKE_CASE - `VOLCANO_COORDS`, `API_CONFIG`
- **Components**: PascalCase - `AccessibleText`, `ModernCard`, `HeroCard`

### Props and Interfaces
- **Interface Names**: PascalCase with descriptive suffix
  - `VolcanoStatusProps`, `ModernCardProps`, `AccessibleButtonProps`
- **Prop Naming**: camelCase, descriptive and specific
  - `gradientColors`, `onPress`, `accessibilityLabel`

## Component Patterns

### Component Structure
```tsx
/**
 * Component description
 * Purpose and usage context
 */

import React from 'react';
import { ... } from 'react-native';
import { ... } from '@/...';

interface ComponentProps {
  // Props with TSDoc comments
  title: string;
  onPress?: () => void;
  style?: ViewStyle;
}

export function ComponentName({
  title,
  onPress,
  style,
}: ComponentProps) {
  // Component logic

  return (
    // JSX with proper formatting
  );
}

const styles = StyleSheet.create({
  // Styles at bottom of file
});
```

### Import Organization
```tsx
// 1. React and React Native
import React, { useState, useCallback } from 'react';
import { View, StyleSheet, Alert } from 'react-native';

// 2. External libraries
import { router } from 'expo-router';
import { LinearGradient } from 'expo-linear-gradient';

// 3. Internal modules (using @/ alias)
import { AccessibleText } from '@/components/ui/AccessibleText';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';

// 4. Relative imports
import './styles.css';
```

## Styling Conventions

### StyleSheet Organization
- **StyleSheet.create()** at bottom of component file
- **Descriptive names** - `container`, `header`, `actionButton`, `alertCard`
- **Consistent spacing** using `Spacing` constants from `@/constants/Layout`
- **Color references** from `@/constants/Colors`

### Style Naming
```tsx
const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: colors.background,
  },
  
  headerSection: {
    padding: Spacing.md,
    marginBottom: Spacing.lg,
  },
  
  actionButton: {
    borderRadius: BorderRadius.md,
    paddingHorizontal: Spacing.lg,
  },
  
  alertCardEmergency: {
    borderLeftColor: colors.alertRed,
    borderLeftWidth: 4,
  },
});
```

## Accessibility Conventions

### Component Usage
- **AccessibleText** for all text elements with proper variants
- **AccessibleButton** for all interactive elements
- **Proper semantic roles** - `button`, `header`, `alert`
- **Descriptive labels** - `accessibilityLabel`, `accessibilityHint`

### Example Implementation
```tsx
<AccessibleText 
  variant="h3" 
  style={styles.sectionTitle}
  accessibilityRole="header"
>
  Emergency Actions
</AccessibleText>

<AccessibleButton
  onPress={handleEmergency}
  accessibilityLabel="Emergency button - Call for immediate help"
  accessibilityHint="Activates emergency protocols and calls emergency services"
  variant="emergency"
>
  EMERGENCIA
</AccessibleButton>
```

## Error Handling Patterns

### API Calls
```tsx
const { data, isLoading, error } = useQuery({
  queryKey: ['volcanoStatus'],
  queryFn: () => apiService.getCurrentAlert(),
  staleTime: 5 * 60 * 1000, // 5 minutes
  retry: 3,
});

if (error) {
  return <ErrorBoundary error={error} />;
}
```

### Component Error Boundaries
```tsx
try {
  // Risky operation
} catch (error) {
  console.error('Operation failed:', error);
  Alert.alert('Error', 'Operation failed. Please try again.');
}
```

## Performance Patterns

### React Query Usage
- **Stale time** for caching strategies
- **Background refetch** for real-time data
- **Offline support** with proper cache management

### Component Optimization
```tsx
const MemoizedComponent = React.memo(Component);

const handlePress = useCallback(() => {
  // Handler logic
}, [dependencies]);

const computedValue = useMemo(() => {
  return expensiveCalculation(data);
}, [data]);
```

## Documentation Standards

### Component Documentation
```tsx
/**
 * Modern header component with gradient support
 * 
 * @param title - Main header text
 * @param subtitle - Optional secondary text
 * @param gradient - Enable gradient background
 * @param gradientColors - Custom gradient colors array
 */
```

### Function Documentation
```tsx
/**
 * Calculates distance between two GPS coordinates
 * Uses Haversine formula for accuracy
 * 
 * @param lat1 - First point latitude
 * @param lng1 - First point longitude
 * @param lat2 - Second point latitude  
 * @param lng2 - Second point longitude
 * @returns Distance in kilometers
 */
```

## Platform-Specific Code
```tsx
const styles = StyleSheet.create({
  container: {
    ...Platform.select({
      ios: {
        shadowColor: '#000',
        shadowOffset: { width: 0, height: 2 },
        shadowOpacity: 0.1,
        shadowRadius: 4,
      },
      android: {
        elevation: 4,
      },
    }),
  },
});
```