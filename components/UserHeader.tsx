/**
 * 🌋 Volcano App Mobile - User Header Component
 * Componente para mostrar información del usuario en el header
 */

import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  Modal,
} from 'react-native';

import { useAuth } from '@/hooks/useAuth';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useI18n } from '@/hooks/useI18n';

import { AccessibleText } from '@/components/ui/AccessibleText';
import { ModernIcon } from '@/components/ui/ModernIcon';
import { UserProfile } from '@/components/UserProfile';
import { AuthOnly, GuestOnly } from '@/components/AuthGuard';

import { Colors } from '@/constants/Colors';
import { BorderRadius, Spacing } from '@/constants/Layout';

interface UserHeaderProps {
  showGreeting?: boolean;
  compact?: boolean;
}

export function UserHeader({ showGreeting = true, compact = false }: UserHeaderProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t } = useI18n();
  const { user, isAuthenticated } = useAuth();
  
  const [showProfile, setShowProfile] = useState(false);

  const handleProfilePress = () => {
    setShowProfile(true);
  };

  const handleCloseProfile = () => {
    setShowProfile(false);
  };

  if (compact) {
    return (
      <View style={styles.compactContainer}>
        <AuthOnly>
          <TouchableOpacity
            onPress={handleProfilePress}
            style={[styles.compactButton, { backgroundColor: colors.tint + '20' }]}
          >
            <View style={[styles.compactAvatar, { backgroundColor: colors.tint }]}>
              <ModernIcon
                name="user"
                size={16}
                color="white"
              />
            </View>
            <AccessibleText
              variant="bodySmall"
              style={[styles.compactName, { color: colors.text }]}
              numberOfLines={1}
            >
              {user?.name || t('user.anonymous')}
            </AccessibleText>
          </TouchableOpacity>
        </AuthOnly>

        <GuestOnly>
          <TouchableOpacity
            onPress={() => {
              // TODO: Navegar a login
            }}
            style={[styles.compactButton, { backgroundColor: colors.border }]}
          >
            <ModernIcon
              name="log-in"
              size={16}
              color={colors.icon}
              style={styles.compactIcon}
            />
            <AccessibleText
              variant="bodySmall"
              color="secondary"
            >
              {t('auth.signIn')}
            </AccessibleText>
          </TouchableOpacity>
        </GuestOnly>

        {/* Modal de perfil */}
        <Modal
          visible={showProfile}
          animationType="slide"
          presentationStyle="pageSheet"
          onRequestClose={handleCloseProfile}
        >
          <UserProfile onClose={handleCloseProfile} />
        </Modal>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <AuthOnly>
        <View style={styles.userSection}>
          {showGreeting && (
            <AccessibleText
              variant="bodySmall"
              color="secondary"
              style={styles.greeting}
            >
              {getGreeting()}
            </AccessibleText>
          )}
          
          <TouchableOpacity
            onPress={handleProfilePress}
            style={styles.userButton}
          >
            <View style={styles.userInfo}>
              <AccessibleText
                variant="subtitle"
                style={[styles.userName, { color: colors.text }]}
                numberOfLines={1}
              >
                {user?.name || t('user.anonymous')}
              </AccessibleText>
              
              {user?.email && (
                <AccessibleText
                  variant="bodySmall"
                  color="secondary"
                  style={styles.userEmail}
                  numberOfLines={1}
                >
                  {user.email}
                </AccessibleText>
              )}
            </View>

            <View style={[styles.avatar, { backgroundColor: colors.tint }]}>
              <ModernIcon
                name="user"
                size={20}
                color="white"
              />
            </View>
          </TouchableOpacity>
        </View>
      </AuthOnly>

      <GuestOnly>
        <View style={styles.guestSection}>
          <View style={styles.guestInfo}>
            <AccessibleText
              variant="subtitle"
              style={[styles.guestTitle, { color: colors.text }]}
            >
              {t('user.welcome')}
            </AccessibleText>
            
            <AccessibleText
              variant="bodySmall"
              color="secondary"
              style={styles.guestMessage}
            >
              {t('user.signInMessage')}
            </AccessibleText>
          </View>

          <TouchableOpacity
            onPress={() => {
              // TODO: Navegar a login
            }}
            style={[styles.signInButton, { backgroundColor: colors.tint }]}
          >
            <ModernIcon
              name="log-in"
              size={16}
              color="white"
            />
          </TouchableOpacity>
        </View>
      </GuestOnly>

      {/* Modal de perfil */}
      <Modal
        visible={showProfile}
        animationType="slide"
        presentationStyle="pageSheet"
        onRequestClose={handleCloseProfile}
      >
        <UserProfile onClose={handleCloseProfile} />
      </Modal>
    </View>
  );
}

// =====================================================
// FUNCIONES AUXILIARES
// =====================================================

function getGreeting(): string {
  const hour = new Date().getHours();
  
  if (hour < 12) {
    return 'Buenos días';
  } else if (hour < 18) {
    return 'Buenas tardes';
  } else {
    return 'Buenas noches';
  }
}

// =====================================================
// ESTILOS
// =====================================================

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
  },
  compactContainer: {
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
  },
  userSection: {},
  guestSection: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  greeting: {
    marginBottom: Spacing.xs,
  },
  userButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  userInfo: {
    flex: 1,
    marginRight: Spacing.md,
  },
  userName: {
    marginBottom: Spacing.xs,
  },
  userEmail: {
    fontStyle: 'italic',
  },
  avatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  guestInfo: {
    flex: 1,
  },
  guestTitle: {
    marginBottom: Spacing.xs,
  },
  guestMessage: {
    lineHeight: 18,
  },
  signInButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    alignItems: 'center',
    justifyContent: 'center',
  },
  compactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: BorderRadius.md,
  },
  compactAvatar: {
    width: 24,
    height: 24,
    borderRadius: 12,
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: Spacing.xs,
  },
  compactName: {
    flex: 1,
    fontSize: 14,
    fontWeight: '500',
  },
  compactIcon: {
    marginRight: Spacing.xs,
  },
});
