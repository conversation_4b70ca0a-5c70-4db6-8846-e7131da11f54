/**
 * LanguageSelector Component
 * Selector de idioma para VolkanApp
 * Soporte: Español, English, Kreyòl Ayisyen
 */

import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  Pressable,
  ScrollView,
  Alert,
} from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useI18n } from '@/hooks/useI18n';
import { Colors } from '@/constants/Colors';
import { BorderRadius, Spacing } from '@/constants/Layout';
import { AccessibleText } from './AccessibleText';
import { SecondaryButton } from './AccessibleButton';
import { ModernCard } from './ModernCard';

interface LanguageSelectorProps {
  showLabel?: boolean;
  compact?: boolean;
  style?: any;
}

export function LanguageSelector({ 
  showLabel = true, 
  compact = false,
  style 
}: LanguageSelectorProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t, currentLanguage, supportedLanguages, setLanguage } = useI18n();
  const [modalVisible, setModalVisible] = useState(false);

  const getCurrentLanguageName = () => {
    const lang = supportedLanguages.find(l => l.code === currentLanguage);
    return lang?.nativeName || 'Español';
  };

  const handleLanguageChange = async (languageCode: string) => {
    try {
      await setLanguage(languageCode);
      setModalVisible(false);
      
      // Mostrar confirmación
      const langName = supportedLanguages.find(l => l.code === languageCode)?.nativeName;
      Alert.alert(
        t('language.change'),
        `${t('language.current')}: ${langName}`,
        [{ text: t('common.ok') }]
      );
    } catch (error) {
      console.error('Error changing language:', error);
      Alert.alert(
        t('common.error'),
        'Error al cambiar idioma',
        [{ text: t('common.ok') }]
      );
    }
  };

  const getLanguageFlag = (code: string) => {
    switch (code) {
      case 'es': return '🇪🇸';
      case 'en': return '🇺🇸';
      case 'ht': return '🇭🇹';
      default: return '🌐';
    }
  };

  const styles = StyleSheet.create({
    container: {
      flexDirection: compact ? 'row' : 'column',
      alignItems: compact ? 'center' : 'flex-start',
    },

    label: {
      marginBottom: compact ? 0 : Spacing.xs,
      marginRight: compact ? Spacing.sm : 0,
    },

    currentLanguage: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingHorizontal: Spacing.sm,
      paddingVertical: Spacing.xs,
      backgroundColor: colors.backgroundSecondary,
      borderRadius: BorderRadius.md,
      borderWidth: 1,
      borderColor: colors.border,
      minWidth: compact ? 80 : 120,
    },

    currentLanguagePressed: {
      backgroundColor: colors.backgroundTertiary,
      borderColor: colors.borderFocus,
    },

    languageFlag: {
      marginRight: Spacing.xs,
      fontSize: 16,
    },

    languageName: {
      flex: 1,
    },

    // Modal styles
    modalOverlay: {
      flex: 1,
      backgroundColor: 'rgba(0, 0, 0, 0.5)',
      justifyContent: 'center',
      alignItems: 'center',
      padding: Spacing.lg,
    },

    modalContent: {
      backgroundColor: colors.background,
      borderRadius: BorderRadius.lg,
      padding: Spacing.lg,
      width: '100%',
      maxWidth: 400,
      maxHeight: '80%',
    },

    modalHeader: {
      marginBottom: Spacing.lg,
      alignItems: 'center',
    },

    languageOption: {
      flexDirection: 'row',
      alignItems: 'center',
      padding: Spacing.md,
      borderRadius: BorderRadius.md,
      marginBottom: Spacing.sm,
      backgroundColor: colors.backgroundSecondary,
      borderWidth: 1,
      borderColor: 'transparent',
    },

    languageOptionSelected: {
      backgroundColor: colors.tint + '20',
      borderColor: colors.tint,
    },

    languageOptionPressed: {
      backgroundColor: colors.backgroundTertiary,
    },

    optionFlag: {
      marginRight: Spacing.md,
      fontSize: 20,
    },

    optionTextContainer: {
      flex: 1,
    },

    optionName: {
      fontWeight: '600',
    },

    optionNativeName: {
      marginTop: 2,
    },

    closeButton: {
      marginTop: Spacing.lg,
    },
  });

  return (
    <View style={[styles.container, style]}>
      {showLabel && !compact && (
        <AccessibleText variant="caption" color="secondary" style={styles.label}>
          {t('language.current')}
        </AccessibleText>
      )}

      <Pressable
        style={({ pressed }) => [
          styles.currentLanguage,
          pressed && styles.currentLanguagePressed,
        ]}
        onPress={() => setModalVisible(true)}
        accessibilityRole="button"
        accessibilityLabel={t('language.select')}
        accessibilityHint={`${t('language.current')}: ${getCurrentLanguageName()}`}
      >
        <AccessibleText style={styles.languageFlag}>
          {getLanguageFlag(currentLanguage)}
        </AccessibleText>
        <AccessibleText 
          variant={compact ? "bodySmall" : "body"} 
          style={styles.languageName}
        >
          {getCurrentLanguageName()}
        </AccessibleText>
      </Pressable>

      <Modal
        visible={modalVisible}
        transparent
        animationType="fade"
        onRequestClose={() => setModalVisible(false)}
      >
        <View style={styles.modalOverlay}>
          <ModernCard style={styles.modalContent}>
            <View style={styles.modalHeader}>
              <AccessibleText variant="h3">
                {t('language.select')}
              </AccessibleText>
            </View>

            <ScrollView showsVerticalScrollIndicator={false}>
              {supportedLanguages.map((language) => (
                <Pressable
                  key={language.code}
                  style={({ pressed }) => [
                    styles.languageOption,
                    currentLanguage === language.code && styles.languageOptionSelected,
                    pressed && styles.languageOptionPressed,
                  ]}
                  onPress={() => handleLanguageChange(language.code)}
                  accessibilityRole="button"
                  accessibilityLabel={`${t('language.change')} ${language.nativeName}`}
                >
                  <AccessibleText style={styles.optionFlag}>
                    {getLanguageFlag(language.code)}
                  </AccessibleText>
                  
                  <View style={styles.optionTextContainer}>
                    <AccessibleText 
                      variant="button" 
                      style={styles.optionName}
                      color={currentLanguage === language.code ? "primary" : "secondary"}
                    >
                      {language.name}
                    </AccessibleText>
                    <AccessibleText 
                      variant="caption" 
                      color="muted" 
                      style={styles.optionNativeName}
                    >
                      {language.nativeName}
                    </AccessibleText>
                  </View>
                </Pressable>
              ))}
            </ScrollView>

            <SecondaryButton
              style={styles.closeButton}
              onPress={() => setModalVisible(false)}
            >
              {t('common.close')}
            </SecondaryButton>
          </ModernCard>
        </View>
      </Modal>
    </View>
  );
}