import { Tabs } from 'expo-router';
import React from 'react';
import { Platform } from 'react-native';

import { HapticTab } from '@/components/HapticTab';
import { HomeIcon, MapIcon, AlertIcon, UsersIcon } from '@/components/ui/ModernIcon';
import TabBarBackground from '@/components/ui/TabBarBackground';
import { Colors } from '@/constants/Colors';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useI18n } from '@/hooks/useI18n';

export default function TabLayout() {
  const colorScheme = useColorScheme();
  const { t } = useI18n();

  return (
    <Tabs
      screenOptions={{
        tabBarActiveTintColor: Colors[colorScheme ?? 'light'].tint,
        headerShown: false,
        tabBarButton: HapticTab,
        tabBarBackground: TabBarBackground,
        tabBarStyle: Platform.select({
          ios: {
            // Use a transparent background on iOS to show the blur effect
            position: 'absolute',
            height: 88,
            paddingBottom: 34,
            paddingTop: 8,
          },
          android: {
            height: 70,
            paddingBottom: 8,
            paddingTop: 8,
            backgroundColor: Colors[colorScheme ?? 'light'].background,
            borderTopWidth: 1,
            borderTopColor: Colors[colorScheme ?? 'light'].border,
            elevation: 8,
          },
          default: {
            height: 70,
            paddingBottom: 8,
            paddingTop: 8,
          },
        }),
        tabBarLabelStyle: {
          fontSize: 12,
          fontWeight: '600',
          marginTop: 4,
        },
      }}>
      <Tabs.Screen
        name="index"
        options={{
          title: t('navigation.home'),
          tabBarIcon: ({ color, size }) => (
            <HomeIcon
              size={size || 24}
              color={color}
              strokeWidth={2.5}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="explore"
        options={{
          title: t('navigation.explore'),
          tabBarIcon: ({ color, size }) => (
            <MapIcon
              size={size || 24}
              color={color}
              strokeWidth={2.5}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="precursors"
        options={{
          title: t('navigation.precursors'),
          tabBarIcon: ({ color, size }) => (
            <AlertIcon
              size={size || 24}
              color={color}
              strokeWidth={2.5}
            />
          ),
        }}
      />
      <Tabs.Screen
        name="family-plan"
        options={{
          title: t('navigation.familyPlan'),
          tabBarIcon: ({ color, size }) => (
            <UsersIcon
              size={size || 24}
              color={color}
              strokeWidth={2.5}
            />
          ),
        }}
      />
    </Tabs>
  );
}
