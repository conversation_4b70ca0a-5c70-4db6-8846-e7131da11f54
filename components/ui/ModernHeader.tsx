/**
 * Modern Header Component
 * Header with proper safe area padding and modern design
 * Inspired by travel app headers
 */

import React from 'react';
import {
  View,
  StyleSheet,
  Platform,
  StatusBar,
  Pressable,
  ViewStyle,
} from 'react-native';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { LinearGradient } from 'expo-linear-gradient';
import { AccessibleText } from './AccessibleText';
import { Colors } from '@/constants/Colors';
import { BorderRadius, Spacing } from '@/constants/Layout';
import { useColorScheme } from '@/hooks/useColorScheme';

interface ModernHeaderProps {
  title: string;
  subtitle?: string;
  leftIcon?: React.ReactNode;
  rightIcon?: React.ReactNode;
  onLeftPress?: () => void;
  onRightPress?: () => void;
  gradient?: boolean;
  gradientColors?: string[];
  transparent?: boolean;
  style?: ViewStyle;
}

export function ModernHeader({
  title,
  subtitle,
  leftIcon,
  rightIcon,
  onLeftPress,
  onRightPress,
  gradient = false,
  gradientColors,
  transparent = false,
  style,
}: ModernHeaderProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const insets = useSafeAreaInsets();

  const headerStyle = [
    styles.container,
    {
      paddingTop: Math.max(insets.top, Platform.OS === 'ios' ? 44 : StatusBar.currentHeight || 24),
      backgroundColor: transparent ? 'transparent' : colors.background,
    },
    style,
  ];

  const defaultGradientColors = gradientColors || colors.gradientPrimary;

  const HeaderContent = () => (
    <>
      {gradient && !transparent && (
        <LinearGradient
          colors={defaultGradientColors}
          style={StyleSheet.absoluteFillObject}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      )}
      
      <View style={styles.content}>
        {/* Left Side */}
        <View style={styles.leftSide}>
          {leftIcon && onLeftPress ? (
            <Pressable
              onPress={onLeftPress}
              style={({ pressed }) => [
                styles.iconButton,
                pressed && styles.iconPressed,
              ]}
              accessibilityRole="button"
            >
              {leftIcon}
            </Pressable>
          ) : leftIcon ? (
            <View style={styles.iconContainer}>
              {leftIcon}
            </View>
          ) : null}
        </View>

        {/* Center Content */}
        <View style={styles.center}>
          <AccessibleText 
            variant="h3" 
            style={[
              styles.title,
              { color: gradient || transparent ? 'white' : colors.text }
            ]}
          >
            {title}
          </AccessibleText>
          {subtitle && (
            <AccessibleText 
              variant="caption" 
              style={[
                styles.subtitle,
                { color: gradient || transparent ? 'rgba(255,255,255,0.8)' : colors.textSecondary }
              ]}
            >
              {subtitle}
            </AccessibleText>
          )}
        </View>

        {/* Right Side */}
        <View style={styles.rightSide}>
          {rightIcon && onRightPress ? (
            <Pressable
              onPress={onRightPress}
              style={({ pressed }) => [
                styles.iconButton,
                pressed && styles.iconPressed,
              ]}
              accessibilityRole="button"
            >
              {rightIcon}
            </Pressable>
          ) : rightIcon ? (
            <View style={styles.iconContainer}>
              {rightIcon}
            </View>
          ) : null}
        </View>
      </View>
    </>
  );

  return (
    <View style={headerStyle}>
      <HeaderContent />
      
      {/* Bottom border only if not transparent */}
      {!transparent && !gradient && (
        <View style={[styles.border, { backgroundColor: colors.border }]} />
      )}
      
      {/* Add subtle shadow for gradient headers */}
      {gradient && (
        <View style={[styles.shadow, { backgroundColor: colors.elevationLight }]} />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'relative',
    zIndex: 1000,
  },

  content: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: Spacing.md,
    paddingBottom: Spacing.md,
    minHeight: 56,
  },

  leftSide: {
    flex: 1,
    alignItems: 'flex-start',
  },

  center: {
    flex: 3,
    alignItems: 'center',
    justifyContent: 'center',
  },

  rightSide: {
    flex: 1,
    alignItems: 'flex-end',
  },

  title: {
    fontWeight: '700',
    textAlign: 'center',
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },

  subtitle: {
    textAlign: 'center',
    marginTop: 2,
    textShadowColor: 'rgba(0, 0, 0, 0.1)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 1,
  },

  iconButton: {
    padding: Spacing.xs,
    borderRadius: BorderRadius.md,
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 40,
    minHeight: 40,
  },

  iconContainer: {
    padding: Spacing.xs,
    justifyContent: 'center',
    alignItems: 'center',
    minWidth: 40,
    minHeight: 40,
  },

  iconPressed: {
    opacity: 0.7,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },

  border: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 1,
  },

  shadow: {
    position: 'absolute',
    bottom: -4,
    left: 0,
    right: 0,
    height: 4,
    opacity: 0.1,
  },
});