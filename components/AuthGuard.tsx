/**
 * 🌋 Volcano App Mobile - Auth Guard Component
 * Componente para proteger rutas que requieren autenticación
 */

import React, { useEffect } from 'react';
import { View, StyleSheet, ActivityIndicator } from 'react-native';
import { router } from 'expo-router';

import { useAuth } from '@/hooks/useAuth';
import { useColorScheme } from '@/hooks/useColorScheme';
import { useI18n } from '@/hooks/useI18n';

import { AccessibleText } from '@/components/ui/AccessibleText';
import { PrimaryButton } from '@/components/ui/AccessibleButton';
import { ModernCard } from '@/components/ui/ModernCard';
import { ModernIcon } from '@/components/ui/ModernIcon';

import { Colors } from '@/constants/Colors';
import { Spacing } from '@/constants/Layout';

// =====================================================
// TIPOS
// =====================================================

interface AuthGuardProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  redirectTo?: string;
  requireAuth?: boolean;
}

// =====================================================
// COMPONENTE PRINCIPAL
// =====================================================

export function AuthGuard({
  children,
  fallback,
  redirectTo = '/(auth)/login',
  requireAuth = true,
}: AuthGuardProps) {
  const { isAuthenticated, isInitializing, user } = useAuth();
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t } = useI18n();

  // Redireccionar si no está autenticado
  useEffect(() => {
    if (!isInitializing && requireAuth && !isAuthenticated) {
      console.log('🔐 AuthGuard: Redirecting to login');
      router.replace(redirectTo);
    }
  }, [isAuthenticated, isInitializing, requireAuth, redirectTo]);

  // Mostrar loading durante inicialización
  if (isInitializing) {
    return (
      <View style={styles.container}>
        <LoadingScreen />
      </View>
    );
  }

  // Si requiere autenticación pero no está autenticado
  if (requireAuth && !isAuthenticated) {
    return (
      <View style={styles.container}>
        {fallback || <AuthRequiredScreen />}
      </View>
    );
  }

  // Si está autenticado o no requiere autenticación, mostrar contenido
  return <>{children}</>;
}

// =====================================================
// COMPONENTES AUXILIARES
// =====================================================

function LoadingScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t } = useI18n();

  return (
    <View style={styles.centerContainer}>
      <ModernCard style={styles.loadingCard}>
        <View style={styles.loadingContent}>
          <ModernIcon
            name="volcano"
            size={48}
            color={colors.tint}
            style={styles.loadingIcon}
          />
          <ActivityIndicator
            size="large"
            color={colors.tint}
            style={styles.loadingSpinner}
          />
          <AccessibleText
            variant="body"
            color="secondary"
            style={styles.loadingText}
          >
            {t('auth.loading.initializing')}
          </AccessibleText>
        </View>
      </ModernCard>
    </View>
  );
}

function AuthRequiredScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t } = useI18n();

  const handleLogin = () => {
    router.push('/(auth)/login');
  };

  const handleRegister = () => {
    router.push('/(auth)/register');
  };

  return (
    <View style={styles.centerContainer}>
      <ModernCard style={styles.authRequiredCard}>
        <View style={styles.authRequiredContent}>
          <ModernIcon
            name="lock"
            size={64}
            color={colors.tint}
            style={styles.authRequiredIcon}
          />
          
          <AccessibleText
            variant="title"
            style={[styles.authRequiredTitle, { color: colors.text }]}
          >
            {t('auth.required.title')}
          </AccessibleText>
          
          <AccessibleText
            variant="body"
            color="secondary"
            style={styles.authRequiredMessage}
          >
            {t('auth.required.message')}
          </AccessibleText>

          <View style={styles.authRequiredButtons}>
            <PrimaryButton
              onPress={handleLogin}
              style={styles.authRequiredButton}
            >
              {t('auth.required.signIn')}
            </PrimaryButton>
            
            <PrimaryButton
              onPress={handleRegister}
              style={styles.authRequiredButton}
            >
              {t('auth.required.signUp')}
            </PrimaryButton>
          </View>
        </View>
      </ModernCard>
    </View>
  );
}

// =====================================================
// HOOKS PERSONALIZADOS
// =====================================================

/**
 * Hook para verificar si una ruta requiere autenticación
 */
export function useRouteAuth(requireAuth: boolean = true) {
  const { isAuthenticated, isInitializing } = useAuth();

  return {
    isAuthenticated,
    isInitializing,
    canAccess: !requireAuth || isAuthenticated,
    shouldRedirect: requireAuth && !isInitializing && !isAuthenticated,
  };
}

/**
 * Hook para proteger componentes específicos
 */
export function useAuthProtection() {
  const { isAuthenticated, isInitializing, user } = useAuth();

  return {
    isAuthenticated,
    isInitializing,
    user,
    isReady: !isInitializing,
    requiresAuth: !isAuthenticated && !isInitializing,
  };
}

// =====================================================
// COMPONENTES DE CONVENIENCIA
// =====================================================

/**
 * Componente para mostrar contenido solo a usuarios autenticados
 */
interface AuthOnlyProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function AuthOnly({ children, fallback }: AuthOnlyProps) {
  const { isAuthenticated, isInitializing } = useAuth();

  if (isInitializing) {
    return null; // O un loading spinner pequeño
  }

  if (!isAuthenticated) {
    return <>{fallback || null}</>;
  }

  return <>{children}</>;
}

/**
 * Componente para mostrar contenido solo a usuarios no autenticados
 */
interface GuestOnlyProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function GuestOnly({ children, fallback }: GuestOnlyProps) {
  const { isAuthenticated, isInitializing } = useAuth();

  if (isInitializing) {
    return null;
  }

  if (isAuthenticated) {
    return <>{fallback || null}</>;
  }

  return <>{children}</>;
}

// =====================================================
// ESTILOS
// =====================================================

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  centerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: Spacing.lg,
  },
  loadingCard: {
    width: '100%',
    maxWidth: 300,
  },
  loadingContent: {
    alignItems: 'center',
    padding: Spacing.xl,
  },
  loadingIcon: {
    marginBottom: Spacing.md,
  },
  loadingSpinner: {
    marginBottom: Spacing.md,
  },
  loadingText: {
    textAlign: 'center',
  },
  authRequiredCard: {
    width: '100%',
    maxWidth: 400,
  },
  authRequiredContent: {
    alignItems: 'center',
    padding: Spacing.xl,
  },
  authRequiredIcon: {
    marginBottom: Spacing.lg,
  },
  authRequiredTitle: {
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  authRequiredMessage: {
    marginBottom: Spacing.xl,
    textAlign: 'center',
    lineHeight: 24,
  },
  authRequiredButtons: {
    width: '100%',
    gap: Spacing.md,
  },
  authRequiredButton: {
    width: '100%',
  },
});
