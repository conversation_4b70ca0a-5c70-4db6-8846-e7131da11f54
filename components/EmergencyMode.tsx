/**
 * EmergencyMode Component
 * Modo de emergencia extrema con UI ultra-simplificada
 * Se activa automáticamente durante alertas críticas
 */

import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  Modal,
  Animated,
  Vibration,
  Platform,
  Linking,
  Alert,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors } from '@/constants/Colors';
import { EmergencyLayout, Spacing } from '@/constants/Layout';
import { EmergencyTypography } from '@/constants/Typography';
import { AccessibleText } from './ui/AccessibleText';
import { EmergencyButton } from './ui/AccessibleButton';

export interface EmergencyAction {
  id: string;
  label: string;
  icon: string;
  action: () => void;
  critical?: boolean;
}

export interface EmergencyModeProps {
  visible: boolean;
  title: string;
  message: string;
  actions: EmergencyAction[];
  onClose?: () => void;
  autoActivated?: boolean;
}

export function EmergencyMode({
  visible,
  title,
  message,
  actions,
  onClose,
  autoActivated = false,
}: EmergencyModeProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [pulseAnim] = useState(new Animated.Value(1));

  useEffect(() => {
    if (visible) {
      // Efectos de emergencia
      if (Platform.OS !== 'web') {
        // Vibración de emergencia
        Vibration.vibrate([0, 1000, 500, 1000, 500, 1000]);
      }

      if (Platform.OS === 'ios') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      }

      // Animación de pulso para llamar la atención
      const pulse = () => {
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.1,
            duration: 800,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 800,
            useNativeDriver: true,
          }),
        ]).start(() => {
          if (visible) pulse();
        });
      };
      pulse();
    }
  }, [visible]);

  // Acciones predefinidas de emergencia
  const emergencyActions: EmergencyAction[] = [
    {
      id: 'call-emergency',
      label: 'LLAMAR 133',
      icon: '📞',
      action: () => {
        if (Platform.OS !== 'web') {
          Linking.openURL('tel:133').catch(() => {
            Alert.alert('Error', 'No se pudo realizar la llamada');
          });
        } else {
          Alert.alert('Emergencia', 'Llame al 133 desde su teléfono');
        }
      },
      critical: true,
    },
    {
      id: 'evacuation-routes',
      label: 'RUTAS DE EVACUACIÓN',
      icon: '🚨',
      action: () => {
        // Navegar a mapa de evacuación
        Alert.alert('Navegando', 'Abriendo rutas de evacuación...');
      },
      critical: true,
    },
    {
      id: 'safe-locations',
      label: 'LUGARES SEGUROS',
      icon: '🏠',
      action: () => {
        // Mostrar refugios y lugares seguros
        Alert.alert('Navegando', 'Mostrando lugares seguros cercanos...');
      },
    },
    {
      id: 'emergency-contacts',
      label: 'CONTACTOS DE EMERGENCIA',
      icon: '👥',
      action: () => {
        // Lista de contactos de emergencia
        Alert.alert('Navegando', 'Abriendo contactos de emergencia...');
      },
    },
  ];

  const allActions = [...emergencyActions, ...actions];

  const styles = StyleSheet.create({
    overlay: {
      position: 'absolute',
      top: 0,
      left: 0,
      right: 0,
      bottom: 0,
      backgroundColor: colors.error,
      justifyContent: 'center',
      alignItems: 'center',
    },

    container: {
      ...EmergencyLayout.container,
      backgroundColor: colors.error,
    },

    header: {
      alignItems: 'center',
      marginBottom: Spacing['3xl'],
    },

    title: {
      ...EmergencyTypography.title,
      color: colors.textInverse,
      textAlign: 'center',
      marginBottom: Spacing.lg,
    },

    message: {
      ...EmergencyTypography.instruction,
      color: colors.textInverse,
      textAlign: 'center',
      marginBottom: Spacing['2xl'],
    },

    actionsContainer: {
      width: '100%',
      alignItems: 'center',
    },

    actionButton: {
      ...EmergencyLayout.button,
      backgroundColor: colors.background,
      marginBottom: Spacing.lg,
      width: '100%',
    },

    criticalButton: {
      backgroundColor: colors.textInverse,
      borderWidth: 4,
      borderColor: colors.background,
    },

    closeButton: {
      position: 'absolute',
      top: 50,
      right: Spacing.lg,
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      width: 60,
      height: 60,
      borderRadius: 30,
      justifyContent: 'center',
      alignItems: 'center',
    },

    autoActivatedBadge: {
      position: 'absolute',
      top: 50,
      left: Spacing.lg,
      backgroundColor: 'rgba(255, 255, 255, 0.2)',
      paddingHorizontal: Spacing.md,
      paddingVertical: Spacing.sm,
      borderRadius: 20,
    },

    badgeText: {
      color: colors.textInverse,
      fontSize: 12,
      fontWeight: 'bold',
    },
  });

  if (!visible) return null;

  return (
    <Modal
      visible={visible}
      animationType="fade"
      statusBarTranslucent={true}
      statusBarBackgroundColor={colors.error}
    >
      <View style={styles.overlay}>
        {/* Badge de activación automática */}
        {autoActivated && (
          <View style={styles.autoActivatedBadge}>
            <AccessibleText style={styles.badgeText}>
              ACTIVADO AUTOMÁTICAMENTE
            </AccessibleText>
          </View>
        )}

        {/* Botón de cerrar (solo si no es crítico) */}
        {onClose && (
          <EmergencyButton
            style={styles.closeButton}
            onPress={onClose}
            accessibilityLabel="Cerrar modo de emergencia"
          >
            ✕
          </EmergencyButton>
        )}

        <Animated.View 
          style={[
            styles.container,
            { transform: [{ scale: pulseAnim }] }
          ]}
        >
          {/* Header */}
          <View style={styles.header}>
            <AccessibleText style={styles.title}>
              🆘 {title}
            </AccessibleText>
            <AccessibleText style={styles.message}>
              {message}
            </AccessibleText>
          </View>

          {/* Acciones de emergencia */}
          <View style={styles.actionsContainer}>
            {allActions.map((action) => (
              <EmergencyButton
                key={action.id}
                style={[
                  styles.actionButton,
                  action.critical && styles.criticalButton,
                ]}
                onPress={action.action}
                accessibilityLabel={`Acción de emergencia: ${action.label}`}
                hapticFeedback={true}
              >
                <AccessibleText
                  style={{
                    fontSize: 24,
                    fontWeight: 'bold',
                    color: action.critical ? colors.error : colors.text,
                    textAlign: 'center',
                  }}
                >
                  {action.icon} {action.label}
                </AccessibleText>
              </EmergencyButton>
            ))}
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
}

// Hook para gestionar el modo de emergencia
export function useEmergencyMode() {
  const [isActive, setIsActive] = useState(false);
  const [emergencyData, setEmergencyData] = useState<{
    title: string;
    message: string;
    actions: EmergencyAction[];
    autoActivated: boolean;
  } | null>(null);

  const activateEmergencyMode = (
    title: string,
    message: string,
    actions: EmergencyAction[] = [],
    autoActivated: boolean = false
  ) => {
    setEmergencyData({
      title,
      message,
      actions,
      autoActivated,
    });
    setIsActive(true);
  };

  const deactivateEmergencyMode = () => {
    setIsActive(false);
    setEmergencyData(null);
  };

  // Activación automática basada en nivel de alerta
  const checkAutoActivation = (alertLevel: string) => {
    if (alertLevel === 'EMERGENCY' && !isActive) {
      activateEmergencyMode(
        'EMERGENCIA VOLCÁNICA',
        'ERUPCIÓN EN CURSO O INMINENTE\nSIGA LAS INSTRUCCIONES OFICIALES',
        [],
        true
      );
    }
  };

  return {
    isActive,
    emergencyData,
    activateEmergencyMode,
    deactivateEmergencyMode,
    checkAutoActivation,
  };
}

// Componente de botón de pánico para activación manual
export function PanicButton({ onActivate }: { onActivate: () => void }) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const [pressCount, setPressCount] = useState(0);

  const handlePress = () => {
    const newCount = pressCount + 1;
    setPressCount(newCount);

    if (newCount >= 3) {
      // Activar después de 3 toques rápidos
      onActivate();
      setPressCount(0);
    } else {
      // Reset después de 2 segundos
      setTimeout(() => setPressCount(0), 2000);
    }

    if (Platform.OS === 'ios') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Heavy);
    }
  };

  return (
    <EmergencyButton
      onPress={handlePress}
      style={{
        position: 'absolute',
        bottom: 100,
        right: 20,
        width: 80,
        height: 80,
        borderRadius: 40,
        backgroundColor: colors.error,
        justifyContent: 'center',
        alignItems: 'center',
        zIndex: 1000,
      }}
      accessibilityLabel={`Botón de pánico. Presione 3 veces para activar modo de emergencia. Presionado ${pressCount} veces.`}
    >
      <AccessibleText style={{ fontSize: 32 }}>🆘</AccessibleText>
      {pressCount > 0 && (
        <AccessibleText style={{ 
          fontSize: 12, 
          color: colors.textInverse,
          position: 'absolute',
          bottom: 5,
        }}>
          {3 - pressCount}
        </AccessibleText>
      )}
    </EmergencyButton>
  );
}
