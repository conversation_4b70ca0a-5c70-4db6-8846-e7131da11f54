# Task Completion Guidelines

## Pre-Development Checklist

### Before Starting Any Task
1. **Read CLAUDE.md** - Review project-specific guidelines
2. **Check Git Status** - `git status` to see current state
3. **Start Development Server** - `npm start` for hot reload
4. **Understand Requirements** - Read task description thoroughly
5. **Review Existing Code** - Check similar implementations in codebase

## Development Process

### Code Quality Standards
- **TypeScript Compliance** - No type errors allowed
- **Accessibility** - WCAG AA compliance using AccessibleText/Button components
- **Responsive Design** - Test on multiple screen sizes
- **Error Handling** - Comprehensive error boundaries and user feedback
- **Performance** - Optimize for mobile devices and offline scenarios

### Testing Requirements
```bash
npm test              # Jest tests must pass
```
- **Unit Tests** - For utility functions and services
- **Component Tests** - For UI components using React Test Renderer
- **Integration Tests** - For API services and data flow
- **Manual Testing** - UI/UX on iOS, Android, and Web platforms

## Code Review Checklist

### TypeScript & Code Quality
- [ ] No TypeScript errors or warnings
- [ ] Proper type definitions for all props and functions
- [ ] Consistent naming conventions (camel<PERSON>ase, PascalCase)
- [ ] Error handling with try/catch and proper user feedback
- [ ] Performance optimizations (React.memo, useCallback, useMemo where needed)

### Accessibility Compliance
- [ ] AccessibleText used for all text elements
- [ ] AccessibleButton used for all interactive elements  
- [ ] Proper accessibilityLabel and accessibilityHint
- [ ] Semantic roles (button, header, alert)
- [ ] Color contrast meets WCAG AA standards
- [ ] Screen reader navigation tested

### React Native Best Practices
- [ ] Platform-specific code using Platform.select()
- [ ] Safe area handling with SafeAreaProvider/useSafeAreaInsets
- [ ] Proper keyboard handling for forms
- [ ] Optimized images and assets
- [ ] Memory leak prevention (cleanup useEffect, subscriptions)

### UI/UX Standards
- [ ] Consistent with design system (Colors.ts, Layout.ts)
- [ ] Proper spacing using Spacing constants
- [ ] Modern gradients and shadows where appropriate
- [ ] Responsive design tested on multiple screen sizes
- [ ] Loading states and error states implemented
- [ ] Haptic feedback for interactive elements

## Validation Steps

### Before Committing
1. **Run Tests** - `npm test` (all tests pass)
2. **TypeScript Check** - No compilation errors
3. **Device Testing** - Test on iOS and Android simulators
4. **Accessibility Test** - VoiceOver/TalkBack navigation
5. **Performance Check** - No memory leaks or performance issues

### Code Organization
- [ ] Imports organized correctly (external → internal → relative)
- [ ] Components in appropriate directories (ui/ vs feature-specific)
- [ ] Services properly abstracted in services/ directory
- [ ] Types defined in types/ directory
- [ ] Constants used from constants/ directory

### Git Commit Standards
```bash
# Conventional commit format
git commit -m "feat: add voice navigation for emergency alerts"
git commit -m "fix: resolve safe area padding on map screen"
git commit -m "style: update emergency button gradient colors"
```

## Documentation Requirements

### Code Documentation
- **Component Props** - TSDoc comments for all props
- **Complex Functions** - Purpose, parameters, return values
- **Service Methods** - API contracts and error handling
- **Hook Usage** - When and how to use custom hooks

### README Updates
- Update CLAUDE.md if architectural changes
- Document new environment variables
- Update command references if needed

## Platform-Specific Validation

### iOS Testing
- [ ] Simulator testing for iPhone and iPad
- [ ] Safe area handling (notch, home indicator)
- [ ] VoiceOver accessibility testing
- [ ] Haptic feedback functionality

### Android Testing  
- [ ] Emulator testing for various screen sizes
- [ ] Back button handling
- [ ] TalkBack accessibility testing
- [ ] Material Design compliance

### Web Testing
- [ ] Browser compatibility (Chrome, Safari, Firefox)
- [ ] Responsive design breakpoints
- [ ] Keyboard navigation
- [ ] Progressive Web App features

## Performance Validation

### React Query Optimization
- [ ] Proper cache invalidation strategies
- [ ] Background refetch configuration
- [ ] Offline capability testing
- [ ] Loading and error states

### Bundle Size & Performance
- [ ] No unnecessary imports or dependencies
- [ ] Image optimization (WebP, appropriate sizes)
- [ ] Code splitting for large components
- [ ] Memory usage monitoring

## Emergency/Critical Features

### Special Validation for Emergency Features
- [ ] Emergency button accessibility (voice, visual, haptic)
- [ ] Offline functionality tested
- [ ] Multiple language support (if applicable)
- [ ] Error recovery mechanisms
- [ ] Clear, actionable messaging

### Volcano-Specific Features
- [ ] Real-time data accuracy
- [ ] Location precision and privacy
- [ ] Alert level color coding consistency
- [ ] Scientific data visualization accuracy

## Final Checklist

### Before Marking Task Complete
- [ ] All acceptance criteria met
- [ ] Code reviewed and follows conventions
- [ ] Tests passing (unit, integration, manual)
- [ ] Accessibility validated
- [ ] Performance optimized
- [ ] Documentation updated
- [ ] Platform compatibility confirmed
- [ ] Git commit with proper message
- [ ] Ready for code review/deployment