/**
 * 🌋 Volcano App - Network Configuration Utility
 * Configuración centralizada y dinámica de red para desarrollo
 */

import Constants from 'expo-constants';
import { Platform } from 'react-native';

// =====================================================
// CONFIGURACIÓN DE RED
// =====================================================

/**
 * Obtener la IP actual de la máquina de desarrollo
 */
function getCurrentDevIP(): string {
  // Intentar obtener la IP desde Expo
  const hostUri = Constants.expoConfig?.hostUri;
  if (hostUri) {
    return hostUri.split(':')[0];
  }

  // Fallback desde variables de entorno
  const envIP = process.env.EXPO_PUBLIC_DEV_HOST_IP;
  if (envIP) {
    return envIP;
  }

  // Fallback final - IP actual conocida
  return '***********';
}

/**
 * Obtener la URL base para APIs según la plataforma
 */
export function getAPIBaseURL(): string {
  if (!__DEV__) {
    return 'https://your-production-api.com/api';
  }

  const port = 3001; // Puerto unificado del backend

  if (Platform.OS === 'web') {
    // En web, localhost funciona normalmente
    return `http://localhost:${port}/api`;
  } else {
    // En móvil, usar la IP de la máquina de desarrollo
    const ip = getCurrentDevIP();
    return `http://${ip}:${port}/api`;
  }
}

/**
 * Obtener la URL base para WebSocket según la plataforma
 */
export function getWebSocketURL(): string {
  if (!__DEV__) {
    return 'https://your-production-api.com';
  }

  const port = 3001; // Puerto unificado del backend

  if (Platform.OS === 'web') {
    // En web, localhost funciona normalmente
    return `http://localhost:${port}`;
  } else {
    // En móvil, usar la IP de la máquina de desarrollo
    const ip = getCurrentDevIP();
    return `http://${ip}:${port}`;
  }
}

/**
 * Obtener información de configuración de red para debugging
 */
export function getNetworkConfig() {
  const currentIP = getCurrentDevIP();
  const apiURL = getAPIBaseURL();
  const wsURL = getWebSocketURL();

  return {
    platform: Platform.OS,
    isDev: __DEV__,
    currentIP,
    hostUri: Constants.expoConfig?.hostUri,
    envIP: process.env.EXPO_PUBLIC_DEV_HOST_IP,
    apiURL,
    wsURL,
    backendPort: 3001,
  };
}

/**
 * Log de configuración de red para debugging
 */
export function logNetworkConfig(serviceName: string) {
  const config = getNetworkConfig();
  console.log(`🔧 ${serviceName} Network Configuration:`, {
    platform: config.platform,
    apiURL: config.apiURL,
    wsURL: config.wsURL,
    currentIP: config.currentIP,
    isDev: config.isDev
  });
}

// =====================================================
// VALIDACIONES
// =====================================================

/**
 * Validar que la configuración de red sea correcta
 */
export function validateNetworkConfig(): {
  isValid: boolean;
  issues: string[];
  recommendations: string[];
} {
  const config = getNetworkConfig();
  const issues: string[] = [];
  const recommendations: string[] = [];

  // Verificar que tengamos una IP válida
  if (!config.currentIP || config.currentIP === 'localhost') {
    issues.push('No se pudo detectar la IP de desarrollo');
    recommendations.push('Configurar EXPO_PUBLIC_DEV_HOST_IP en .env');
  }

  // Verificar que la IP no sea una IP privada antigua
  if (config.currentIP.startsWith('192.168.1.')) {
    issues.push('IP de desarrollo parece ser de una red anterior');
    recommendations.push('Actualizar IP en .env y configuraciones');
  }

  // Verificar configuración de Expo
  if (!config.hostUri && Platform.OS !== 'web') {
    issues.push('hostUri no disponible desde Expo');
    recommendations.push('Verificar que Expo esté corriendo correctamente');
  }

  return {
    isValid: issues.length === 0,
    issues,
    recommendations
  };
}

export default {
  getAPIBaseURL,
  getWebSocketURL,
  getNetworkConfig,
  logNetworkConfig,
  validateNetworkConfig
};
