# 🛠️ Guía de Desarrollo - Sistema de Autenticación

## Introducción

Esta guía está dirigida a desarrolladores que necesitan trabajar con el sistema de autenticación de VolkanApp Mobile. Aquí encontrarás ejemplos prácticos, patrones de uso y mejores prácticas para implementar funcionalidades que requieran autenticación.

## 🎣 Hooks de Autenticación

### useAuth - Hook Principal

El hook principal para acceder al estado de autenticación:

```typescript
import { useAuth } from '@/hooks/useAuth';

function MyComponent() {
  const {
    // Estado del usuario
    user,
    session,
    isAuthenticated,
    isLoading,
    isInitializing,
    error,
    
    // Métodos de autenticación
    login,
    register,
    logout,
    
    // Métodos de utilidad
    refreshUser,
    updateProfile,
    clearError,
  } = useAuth();

  if (isInitializing) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return <LoginPrompt />;
  }

  return (
    <View>
      <Text>¡Ho<PERSON>, {user?.name}!</Text>
    </View>
  );
}
```

### Hooks Especializados

#### useAuthState - Estado Simplificado

```typescript
import { useAuthState } from '@/hooks/useAuth';

function UserBadge() {
  const {
    user,
    isAuthenticated,
    isLoading,
    userId,
    userEmail,
    userName,
  } = useAuthState();

  if (isLoading) return <Skeleton />;
  
  return (
    <View>
      <Text>{userName || 'Usuario Anónimo'}</Text>
      <Text>{userEmail}</Text>
    </View>
  );
}
```

#### useAuthToken - Token de Autenticación

```typescript
import { useAuthToken } from '@/hooks/useAuth';

function ApiComponent() {
  const token = useAuthToken();

  const fetchData = async () => {
    const response = await fetch('/api/data', {
      headers: {
        'Authorization': `Bearer ${token}`,
      },
    });
    return response.json();
  };

  // El token se actualiza automáticamente
  useEffect(() => {
    if (token) {
      fetchData();
    }
  }, [token]);
}
```

#### useAuthOperations - Operaciones de Auth

```typescript
import { useAuthOperations } from '@/hooks/useAuth';

function AuthForm() {
  const { login, register, logout } = useAuthOperations();
  const [isLoading, setIsLoading] = useState(false);

  const handleLogin = async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const result = await login({ email, password });
      if (result.success) {
        // Login exitoso
        router.push('/(tabs)');
      } else {
        Alert.alert('Error', result.error);
      }
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <LoginForm onSubmit={handleLogin} isLoading={isLoading} />
  );
}
```

## 🛡️ Protección de Rutas

### AuthGuard - Proteger Componentes

#### Uso Básico

```typescript
import { AuthGuard } from '@/components/AuthGuard';

function ProtectedScreen() {
  return (
    <AuthGuard>
      <View>
        <Text>Contenido solo para usuarios autenticados</Text>
      </View>
    </AuthGuard>
  );
}
```

#### Con Fallback Personalizado

```typescript
function ProtectedScreen() {
  return (
    <AuthGuard
      fallback={
        <View>
          <Text>Necesitas iniciar sesión</Text>
          <Button title="Login" onPress={() => router.push('/login')} />
        </View>
      }
    >
      <ProtectedContent />
    </AuthGuard>
  );
}
```

#### Protección Opcional

```typescript
function OptionalAuthScreen() {
  return (
    <AuthGuard requireAuth={false}>
      <View>
        <AuthOnly>
          <Text>Solo usuarios autenticados ven esto</Text>
        </AuthOnly>
        
        <GuestOnly>
          <Text>Solo usuarios no autenticados ven esto</Text>
        </GuestOnly>
        
        <Text>Todos ven esto</Text>
      </View>
    </AuthGuard>
  );
}
```

### Componentes Condicionales

#### AuthOnly y GuestOnly

```typescript
import { AuthOnly, GuestOnly } from '@/components/AuthGuard';

function ConditionalContent() {
  return (
    <View>
      <AuthOnly>
        <UserProfile />
        <LogoutButton />
      </AuthOnly>

      <GuestOnly>
        <LoginButton />
        <RegisterButton />
      </GuestOnly>
    </View>
  );
}
```

### Hooks de Protección

#### useRouteAuth

```typescript
import { useRouteAuth } from '@/components/AuthGuard';

function MyScreen() {
  const { isAuthenticated, canAccess, shouldRedirect } = useRouteAuth(true);

  useEffect(() => {
    if (shouldRedirect) {
      router.replace('/(auth)/login');
    }
  }, [shouldRedirect]);

  if (!canAccess) {
    return <LoadingScreen />;
  }

  return <ProtectedContent />;
}
```

## 👨‍👩‍👧‍👦 Hooks de Planes Familiares

### useFamilyPlanManager - Gestión Completa

```typescript
import { useFamilyPlanManager } from '@/hooks/useFamilyPlan';

function FamilyPlanScreen() {
  const {
    // Datos
    userPlans,
    primaryPlan,
    hasPlans,
    
    // Estados
    isLoading,
    error,
    
    // Mutaciones
    createPlan,
    joinPlan,
    isCreating,
    isJoining,
    
    // Utilidades
    refetch,
  } = useFamilyPlanManager();

  const handleCreatePlan = async () => {
    createPlan({
      name: 'Mi Familia',
      description: 'Plan de emergencia familiar',
      user_info: {
        name: 'Juan Pérez',
        phone: '+56912345678',
        role: 'LEADER',
      },
    });
  };

  if (isLoading) return <LoadingSpinner />;
  if (error) return <ErrorMessage error={error} />;

  return (
    <View>
      {hasPlans ? (
        <FamilyPlanList plans={userPlans} />
      ) : (
        <EmptyState onCreatePlan={handleCreatePlan} />
      )}
    </View>
  );
}
```

### useFamilyPlanDetails - Plan Específico

```typescript
import { useFamilyPlanDetails } from '@/hooks/useFamilyPlan';

function FamilyPlanDetails({ planId }: { planId: string }) {
  const {
    // Datos del plan
    plan,
    
    // Estados
    isLoading,
    error,
    
    // Mutaciones
    addContact,
    addKit,
    addMeetingPoint,
    
    // Estados de mutación
    isAddingContact,
    isAddingKit,
    isAddingMeetingPoint,
    
    // Utilidades
    refetch,
  } = useFamilyPlanDetails(planId);

  const handleAddContact = () => {
    addContact({
      name: 'Dr. García',
      phone: '+56987654321',
      relationship: 'Médico familiar',
      priority: 1,
      is_local: true,
    });
  };

  if (isLoading) return <LoadingSpinner />;
  if (!plan) return <NotFound />;

  return (
    <ScrollView>
      <PlanHeader plan={plan} />
      <MembersList members={plan.members} />
      <ContactsList 
        contacts={plan.contacts} 
        onAddContact={handleAddContact}
        isAdding={isAddingContact}
      />
      <KitsList kits={plan.kits} />
      <MeetingPointsList points={plan.meeting_points} />
    </ScrollView>
  );
}
```

## 🔧 Agregar Nuevas Funcionalidades

### 1. Crear Nueva Pantalla Protegida

```typescript
// app/(protected)/new-feature.tsx
import { AuthGuard } from '@/components/AuthGuard';
import { useAuth } from '@/hooks/useAuth';

export default function NewFeatureScreen() {
  const { user } = useAuth();

  return (
    <AuthGuard>
      <View>
        <Text>Nueva funcionalidad para {user?.name}</Text>
        {/* Tu contenido aquí */}
      </View>
    </AuthGuard>
  );
}
```

### 2. Crear Servicio con Autenticación

```typescript
// services/newService.ts
import { getAuthToken, getCurrentUserId } from '@/services/auth';

class NewService {
  private async getHeaders() {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    const token = await getAuthToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const userId = getCurrentUserId();
    if (userId) {
      headers['X-User-ID'] = userId;
    }

    return headers;
  }

  async fetchUserData() {
    const response = await fetch('/api/user-data', {
      headers: await this.getHeaders(),
    });
    
    if (!response.ok) {
      throw new Error('Failed to fetch user data');
    }
    
    return response.json();
  }
}

export const newService = new NewService();
```

### 3. Crear Hook Personalizado

```typescript
// hooks/useNewFeature.ts
import { useQuery, useMutation } from '@tanstack/react-query';
import { useAuth } from './useAuth';
import { newService } from '@/services/newService';

export function useNewFeature() {
  const { isAuthenticated, user } = useAuth();

  const dataQuery = useQuery({
    queryKey: ['newFeature', user?.id],
    queryFn: () => newService.fetchUserData(),
    enabled: isAuthenticated,
  });

  const updateMutation = useMutation({
    mutationFn: newService.updateData,
    onSuccess: () => {
      dataQuery.refetch();
    },
  });

  return {
    data: dataQuery.data,
    isLoading: dataQuery.isLoading,
    error: dataQuery.error,
    updateData: updateMutation.mutate,
    isUpdating: updateMutation.isPending,
  };
}
```

### 4. Agregar Ruta Protegida al Navigator

```typescript
// components/AuthNavigator.tsx
const PROTECTED_ROUTES = [
  '/(tabs)',
  '/family-plan',
  '/profile',
  '/settings',
  '/new-feature', // ← Agregar aquí
] as const;
```

## 🎨 Componentes de UI con Autenticación

### Botón Condicional

```typescript
function ConditionalButton() {
  const { isAuthenticated } = useAuth();

  if (isAuthenticated) {
    return (
      <PrimaryButton onPress={handleAuthenticatedAction}>
        Acción para Usuario
      </PrimaryButton>
    );
  }

  return (
    <SecondaryButton onPress={() => router.push('/(auth)/login')}>
      Iniciar Sesión
    </SecondaryButton>
  );
}
```

### Header con Usuario

```typescript
function AppHeader() {
  const { user, isAuthenticated } = useAuth();

  return (
    <View style={styles.header}>
      <Text style={styles.title}>VolkanApp</Text>
      
      {isAuthenticated ? (
        <TouchableOpacity onPress={openProfile}>
          <View style={styles.userBadge}>
            <Text>{user?.name}</Text>
            <Avatar source={{ uri: user?.avatar_url }} />
          </View>
        </TouchableOpacity>
      ) : (
        <TouchableOpacity onPress={() => router.push('/(auth)/login')}>
          <Text style={styles.loginText}>Iniciar Sesión</Text>
        </TouchableOpacity>
      )}
    </View>
  );
}
```

## 🔍 Debugging y Testing

### Logs de Autenticación

```typescript
// Habilitar logs detallados
console.log('🔐 Auth State:', {
  isAuthenticated,
  userId: user?.id,
  email: user?.email,
  sessionValid: !!session,
});
```

### Testing de Componentes

```typescript
// __tests__/AuthComponent.test.tsx
import { render } from '@testing-library/react-native';
import { AuthProvider } from '@/providers/AuthProvider';

const renderWithAuth = (component: React.ReactElement, authState = {}) => {
  return render(
    <AuthProvider initialState={authState}>
      {component}
    </AuthProvider>
  );
};

test('shows content when authenticated', () => {
  const { getByText } = renderWithAuth(
    <ProtectedComponent />,
    { isAuthenticated: true, user: { name: 'Test User' } }
  );
  
  expect(getByText('Protected Content')).toBeTruthy();
});
```

## ⚠️ Mejores Prácticas

### 1. Siempre Verificar Estado de Auth

```typescript
// ❌ Malo
function BadComponent() {
  const { user } = useAuth();
  return <Text>{user.name}</Text>; // Puede fallar si user es null
}

// ✅ Bueno
function GoodComponent() {
  const { user, isAuthenticated } = useAuth();
  
  if (!isAuthenticated || !user) {
    return <LoginPrompt />;
  }
  
  return <Text>{user.name}</Text>;
}
```

### 2. Manejar Estados de Carga

```typescript
function LoadingAwareComponent() {
  const { isLoading, isInitializing, isAuthenticated } = useAuth();

  if (isInitializing) {
    return <InitializingSpinner />;
  }

  if (isLoading) {
    return <LoadingSpinner />;
  }

  if (!isAuthenticated) {
    return <AuthRequired />;
  }

  return <AuthenticatedContent />;
}
```

### 3. Usar Hooks Específicos

```typescript
// ❌ Menos eficiente
function UserName() {
  const { user, isAuthenticated, isLoading } = useAuth();
  // Obtiene todo el estado aunque solo necesite el nombre
}

// ✅ Más eficiente
function UserName() {
  const { userName, isAuthenticated, isLoading } = useAuthState();
  // Solo obtiene lo que necesita
}
```

### 4. Limpiar Errores

```typescript
function LoginForm() {
  const { login, error, clearError } = useAuth();

  useEffect(() => {
    // Limpiar errores al montar el componente
    clearError();
  }, []);

  const handleSubmit = async (credentials) => {
    clearError(); // Limpiar errores previos
    await login(credentials);
  };
}
```

## 🚨 Manejo de Errores

### Errores de Red

```typescript
function NetworkErrorHandler() {
  const { error } = useAuth();

  if (error?.includes('network')) {
    return (
      <ErrorBanner>
        <Text>Error de conexión. Verifica tu internet.</Text>
        <Button title="Reintentar" onPress={retry} />
      </ErrorBanner>
    );
  }

  return null;
}
```

### Errores de Autenticación

```typescript
function AuthErrorHandler() {
  const { error, clearError } = useAuth();

  useEffect(() => {
    if (error?.includes('Invalid login credentials')) {
      Alert.alert(
        'Credenciales Incorrectas',
        'Verifica tu email y contraseña',
        [{ text: 'OK', onPress: clearError }]
      );
    }
  }, [error]);
}
```

---

## 📚 Referencias Útiles

- [React Query Documentation](https://tanstack.com/query/latest)
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript)
- [Expo Router Authentication](https://docs.expo.dev/router/reference/authentication/)
- [React Native AsyncStorage](https://react-native-async-storage.github.io/async-storage/)

---

*Última actualización: 3 de enero de 2025*
