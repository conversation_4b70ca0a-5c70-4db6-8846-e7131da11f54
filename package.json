{"name": "volcano-mobile-clean", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "test": "jest --watchAll"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "^2.1.2", "@react-navigation/native": "^7.1.6", "@supabase/supabase-js": "^2.52.1", "@tanstack/react-query": "^5.83.0", "axios": "^1.11.0", "buffer": "^6.0.3", "expo": "~53.0.20", "expo-blur": "~14.1.5", "expo-device": "~7.1.4", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-linear-gradient": "^14.1.5", "expo-linking": "~7.1.7", "expo-localization": "^16.1.6", "expo-location": "~18.1.6", "expo-notifications": "~0.31.4", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "i18next": "^25.3.2", "lucide-react-native": "^0.526.0", "react": "19.0.0", "react-dom": "19.0.0", "react-i18next": "^15.6.1", "react-native": "0.79.5", "react-native-chart-kit": "^6.12.0", "react-native-crypto-js": "^1.0.0", "react-native-get-random-values": "~1.11.0", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "^5.4.0", "react-native-screens": "~4.11.1", "react-native-svg": "^15.11.2", "react-native-url-polyfill": "^2.0.0", "react-native-web": "~0.20.0", "react-native-webview": "^13.13.5", "socket.io-client": "^4.8.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "babel-plugin-module-resolver": "^5.0.2", "jest": "^29.2.1", "jest-expo": "~53.0.9", "react-test-renderer": "19.0.0", "typescript": "~5.8.3"}, "private": true}