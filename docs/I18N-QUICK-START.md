# 🚀 Guía Rápida - Sistema i18n VolkanApp

## Inicio Rápido para Desarrolladores

### 1. Importar Hook
```typescript
import { useI18n } from '@/hooks/useI18n';
```

### 2. Usar en Componente
```typescript
export default function MyComponent() {
  const { t } = useI18n();
  
  return (
    <AccessibleText variant="h1">
      {t('home.title')}
    </AccessibleText>
  );
}
```

### 3. Agregar Selector de Idioma
```typescript
import { LanguageSelector } from '@/components/ui/LanguageSelector';

<ModernHeader
  title={t('screen.title')}
  rightIcon={<LanguageSelector compact showLabel={false} />}
/>
```

## 📋 Comandos Útiles

### Verificar Traducciones
```bash
# Buscar claves sin traducir
grep -r "t(" app/ | grep -v ".json"
```

### Validar JSON
```bash
# Verificar sintaxis JSON
npm run json-validate
```

### Testing i18n
```bash
# Correr tests específicos de i18n
npm test -- i18n
```

## 🔧 Snippets VS Code

### React Component con i18n
```json
{
  "React i18n Component": {
    "prefix": "ri18n",
    "body": [
      "import { useI18n } from '@/hooks/useI18n';",
      "",
      "export default function ${1:ComponentName}() {",
      "  const { t } = useI18n();",
      "",
      "  return (",
      "    <AccessibleText variant=\"${2:h1}\">",
      "      {t('${3:section.key}')}",
      "    </AccessibleText>",
      "  );",
      "}"
    ]
  }
}
```

### Traducción con Variable
```json
{
  "i18n with interpolation": {
    "prefix": "ti18n",
    "body": [
      "{t('${1:section.key}', { ${2:variable}: ${3:value} })}"
    ]
  }
}
```

## 🆘 Problemas Comunes

### Clave no encontrada
```typescript
// ❌ Error
{t('nonexistent.key')}

// ✅ Solución con fallback
{t('nonexistent.key', { defaultValue: 'Texto por defecto' })}
```

### Idioma no cambia
```typescript
// ❌ Problema: AsyncStorage no configurado
// ✅ Solución: Verificar permisos AsyncStorage
```

### Performance lenta
```typescript
// ❌ Problema: Re-renders innecesarios
const text = t('key'); // En render

// ✅ Solución: Memoizar si es costoso
const text = useMemo(() => t('key'), [t]);
```

## 📚 Referencias Rápidas

- **Documentación completa**: `/docs/MULTILINGUAL-SYSTEM.md`
- **Traducciones**: `/i18n/translations/`
- **Hook principal**: `/hooks/useI18n.ts`
- **Configuración**: `/i18n/index.ts`

## 🔗 Enlaces Útiles

- [react-i18next docs](https://react.i18next.com/)
- [expo-localization](https://docs.expo.dev/versions/latest/sdk/localization/)
- [WCAG Guidelines](https://www.w3.org/WAI/WCAG21/quickref/)