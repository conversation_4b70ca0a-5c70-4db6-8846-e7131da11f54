/**
 * 🌋 Volcano App Mobile - Tests para servicio de notificaciones
 * Tests unitarios para el servicio de notificaciones con Supabase Realtime
 */

import { NotificationService } from '../notifications';

// Mock para expo-notifications
jest.mock('expo-notifications', () => ({
  requestPermissionsAsync: jest.fn(),
  getExpoPushTokenAsync: jest.fn(),
  scheduleNotificationAsync: jest.fn(),
  addNotificationReceivedListener: jest.fn(() => ({ remove: jest.fn() })),
  addNotificationResponseReceivedListener: jest.fn(() => ({ remove: jest.fn() })),
  setNotificationHandler: jest.fn()
}));

// Mock para expo-device
jest.mock('expo-device', () => ({
  isDevice: true,
  deviceType: 1,
  deviceName: 'Test Device'
}));

// Mock para expo-haptics
jest.mock('expo-haptics', () => ({
  impactAsync: jest.fn(),
  notificationAsync: jest.fn(),
  ImpactFeedbackStyle: {
    Heavy: 'heavy'
  },
  NotificationFeedbackType: {
    Success: 'success',
    Warning: 'warning',
    Error: 'error'
  }
}));

// Mock para react-native
jest.mock('react-native', () => ({
  Platform: {
    OS: 'ios'
  },
  Vibration: {
    vibrate: jest.fn()
  }
}));

// Mock para supabase
jest.mock('../supabase', () => ({
  initializeSupabase: jest.fn(() => Promise.resolve(true)),
  subscribeToRealtimeNotifications: jest.fn(),
  unsubscribeFromRealtimeNotifications: jest.fn()
}));

describe('NotificationService', () => {
  let notificationService: NotificationService;

  beforeEach(() => {
    jest.clearAllMocks();
    notificationService = new NotificationService();
  });

  describe('Initialization', () => {
    it('initializes successfully with granted permissions', async () => {
      const Notifications = require('expo-notifications');
      Notifications.requestPermissionsAsync.mockResolvedValue({
        status: 'granted',
        canAskAgain: true
      });
      Notifications.getExpoPushTokenAsync.mockResolvedValue({
        data: 'test-push-token'
      });

      const permissions = await notificationService.initialize();

      expect(permissions.status).toBe('granted');
      expect(notificationService.isInitialized).toBe(true);
    });

    it('handles permission denied', async () => {
      const Notifications = require('expo-notifications');
      Notifications.requestPermissionsAsync.mockResolvedValue({
        status: 'denied',
        canAskAgain: false
      });

      const permissions = await notificationService.initialize();

      expect(permissions.status).toBe('denied');
      expect(notificationService.isInitialized).toBe(false);
    });

    it('handles non-device environment', async () => {
      const Device = require('expo-device');
      Device.isDevice = false;

      const permissions = await notificationService.initialize();

      expect(permissions.status).toBe('denied');
      expect(permissions.canAskAgain).toBe(false);
    });

    it('handles Supabase initialization failure', async () => {
      const { initializeSupabase } = require('../supabase');
      initializeSupabase.mockResolvedValue(false);

      const Notifications = require('expo-notifications');
      Notifications.requestPermissionsAsync.mockResolvedValue({
        status: 'granted',
        canAskAgain: true
      });

      const permissions = await notificationService.initialize();

      expect(permissions.status).toBe('granted');
      // Debería continuar sin Supabase
    });
  });

  describe('Permission Management', () => {
    it('requests permissions correctly', async () => {
      const Notifications = require('expo-notifications');
      Notifications.requestPermissionsAsync.mockResolvedValue({
        status: 'granted',
        canAskAgain: true,
        granted: true,
        ios: {
          status: 1,
          allowsAlert: true,
          allowsBadge: true,
          allowsSound: true
        }
      });

      const permissions = await notificationService.requestPermissions();

      expect(Notifications.requestPermissionsAsync).toHaveBeenCalledWith({
        ios: {
          allowAlert: true,
          allowBadge: true,
          allowSound: true,
          allowDisplayInCarPlay: true,
          allowCriticalAlerts: true
        }
      });
      expect(permissions.status).toBe('granted');
    });

    it('handles permission request error', async () => {
      const Notifications = require('expo-notifications');
      Notifications.requestPermissionsAsync.mockRejectedValue(new Error('Permission error'));

      await expect(notificationService.requestPermissions()).rejects.toThrow('Permission error');
    });
  });

  describe('Push Token Management', () => {
    it('registers for push notifications successfully', async () => {
      const Notifications = require('expo-notifications');
      Notifications.getExpoPushTokenAsync.mockResolvedValue({
        data: 'ExponentPushToken[test-token]'
      });

      const token = await notificationService.registerForPushNotifications();

      expect(token).toBe('ExponentPushToken[test-token]');
      expect(notificationService.pushToken).toBe('ExponentPushToken[test-token]');
    });

    it('handles push token error', async () => {
      const Notifications = require('expo-notifications');
      Notifications.getExpoPushTokenAsync.mockRejectedValue(new Error('Token error'));

      const token = await notificationService.registerForPushNotifications();

      expect(token).toBeNull();
    });
  });

  describe('Local Notifications', () => {
    it('schedules local notification correctly', async () => {
      const Notifications = require('expo-notifications');
      Notifications.scheduleNotificationAsync.mockResolvedValue('notification-id');

      const notificationId = await notificationService.scheduleLocalNotification(
        'Test Title',
        'Test Body',
        { priority: 'high' }
      );

      expect(Notifications.scheduleNotificationAsync).toHaveBeenCalledWith({
        content: {
          title: 'Test Title',
          body: 'Test Body',
          priority: 'high'
        },
        trigger: null
      });
      expect(notificationId).toBe('notification-id');
    });

    it('schedules emergency notification with vibration', async () => {
      const Notifications = require('expo-notifications');
      const Haptics = require('expo-haptics');
      const { Vibration } = require('react-native');

      Notifications.scheduleNotificationAsync.mockResolvedValue('emergency-id');

      await notificationService.showEmergencyNotification(
        'Emergency Alert',
        'Volcanic activity detected'
      );

      expect(Haptics.impactAsync).toHaveBeenCalledWith(Haptics.ImpactFeedbackStyle.Heavy);
      expect(Vibration.vibrate).toHaveBeenCalledWith([0, 250, 250, 250]);
    });

    it('handles notification scheduling error', async () => {
      const Notifications = require('expo-notifications');
      Notifications.scheduleNotificationAsync.mockRejectedValue(new Error('Schedule error'));

      const notificationId = await notificationService.scheduleLocalNotification(
        'Test Title',
        'Test Body'
      );

      expect(notificationId).toBeNull();
    });
  });

  describe('Realtime Notifications', () => {
    it('sets up realtime subscription correctly', () => {
      const { subscribeToRealtimeNotifications } = require('../supabase');

      notificationService.setupRealtimeSubscription();

      expect(subscribeToRealtimeNotifications).toHaveBeenCalledWith(
        expect.any(Function)
      );
    });

    it('handles realtime notification correctly', async () => {
      const Notifications = require('expo-notifications');
      Notifications.scheduleNotificationAsync.mockResolvedValue('realtime-id');

      const mockNotification = {
        id: '1',
        title: 'Volcanic Alert',
        message: 'Yellow alert issued',
        alert_level: 'YELLOW',
        created_at: new Date().toISOString()
      };

      // Simular callback de notificación en tiempo real
      const { subscribeToRealtimeNotifications } = require('../supabase');
      const callback = subscribeToRealtimeNotifications.mock.calls[0]?.[0];

      if (callback) {
        await callback(mockNotification);
      }

      expect(Notifications.scheduleNotificationAsync).toHaveBeenCalled();
    });

    it('unsubscribes from realtime correctly', () => {
      const { unsubscribeFromRealtimeNotifications } = require('../supabase');

      notificationService.cleanup();

      expect(unsubscribeFromRealtimeNotifications).toHaveBeenCalled();
    });
  });

  describe('Notification Listeners', () => {
    it('sets up notification listeners correctly', () => {
      const Notifications = require('expo-notifications');

      notificationService.setupNotificationListeners();

      expect(Notifications.addNotificationReceivedListener).toHaveBeenCalled();
      expect(Notifications.addNotificationResponseReceivedListener).toHaveBeenCalled();
    });

    it('handles received notification', () => {
      const Notifications = require('expo-notifications');
      const mockListener = jest.fn();

      Notifications.addNotificationReceivedListener.mockImplementation((callback) => {
        callback({
          request: {
            content: {
              title: 'Test Notification',
              body: 'Test Body'
            }
          }
        });
        return { remove: jest.fn() };
      });

      notificationService.setupNotificationListeners();

      expect(Notifications.addNotificationReceivedListener).toHaveBeenCalled();
    });

    it('handles notification response', () => {
      const Notifications = require('expo-notifications');

      Notifications.addNotificationResponseReceivedListener.mockImplementation((callback) => {
        callback({
          notification: {
            request: {
              content: {
                title: 'Test Notification',
                body: 'Test Body'
              }
            }
          },
          actionIdentifier: 'default'
        });
        return { remove: jest.fn() };
      });

      notificationService.setupNotificationListeners();

      expect(Notifications.addNotificationResponseReceivedListener).toHaveBeenCalled();
    });
  });

  describe('Cleanup', () => {
    it('cleans up resources correctly', () => {
      const { unsubscribeFromRealtimeNotifications } = require('../supabase');

      // Configurar listeners
      notificationService.setupNotificationListeners();
      notificationService.setupRealtimeSubscription();

      // Limpiar
      notificationService.cleanup();

      expect(unsubscribeFromRealtimeNotifications).toHaveBeenCalled();
    });

    it('handles cleanup when not initialized', () => {
      expect(() => {
        notificationService.cleanup();
      }).not.toThrow();
    });
  });

  describe('Error Handling', () => {
    it('handles initialization error gracefully', async () => {
      const Notifications = require('expo-notifications');
      Notifications.requestPermissionsAsync.mockRejectedValue(new Error('Init error'));

      await expect(notificationService.initialize()).rejects.toThrow('Init error');
    });

    it('handles missing push token gracefully', async () => {
      const Notifications = require('expo-notifications');
      Notifications.getExpoPushTokenAsync.mockResolvedValue({ data: null });

      const token = await notificationService.registerForPushNotifications();

      expect(token).toBeNull();
    });
  });

  describe('Platform Specific Behavior', () => {
    it('handles iOS specific features', async () => {
      const { Platform } = require('react-native');
      Platform.OS = 'ios';

      const permissions = await notificationService.requestPermissions();

      expect(permissions).toBeDefined();
    });

    it('handles Android specific features', async () => {
      const { Platform } = require('react-native');
      Platform.OS = 'android';

      const permissions = await notificationService.requestPermissions();

      expect(permissions).toBeDefined();
    });
  });
});
