/**
 * 🌋 Volcano App Mobile - Family Plan Screen
 * Pantalla principal de planes familiares de emergencia
 */

import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
  RefreshControl,
} from 'react-native';

import { useColorScheme } from '@/hooks/useColorScheme';
import { useI18n } from '@/hooks/useI18n';
import { useFamilyPlanManager } from '@/hooks/useFamilyPlan';

import { AccessibleText } from '@/components/ui/AccessibleText';
import { PrimaryButton, SecondaryButton } from '@/components/ui/AccessibleButton';
import { ModernCard } from '@/components/ui/ModernCard';
import { ModernHeader } from '@/components/ui/ModernHeader';
import { ModernIcon } from '@/components/ui/ModernIcon';
import { AuthGuard } from '@/components/AuthGuard';

import { Colors } from '@/constants/Colors';
import { Spacing } from '@/constants/Layout';

export default function FamilyPlanScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t } = useI18n();
  
  const {
    userPlans,
    primaryPlan,
    hasPlans,
    isLoading,
    createPlan,
    joinPlan,
    isCreating,
    isJoining,
    createError,
    joinError,
    refetch,
  } = useFamilyPlanManager();

  const [showCreateForm, setShowCreateForm] = useState(false);
  const [showJoinForm, setShowJoinForm] = useState(false);

  const handleCreatePlan = () => {
    setShowCreateForm(true);
  };

  const handleJoinPlan = () => {
    setShowJoinForm(true);
  };

  const handleRefresh = () => {
    refetch();
  };

  return (
    <AuthGuard>
      <View style={styles.container}>
        <ModernHeader
          title={t('familyPlan.title')}
          subtitle={t('familyPlan.subtitle')}
        />

        <ScrollView
          style={styles.content}
          showsVerticalScrollIndicator={false}
          refreshControl={
            <RefreshControl
              refreshing={isLoading}
              onRefresh={handleRefresh}
              tintColor={colors.tint}
            />
          }
        >
          {/* Estado sin planes */}
          {!hasPlans && !isLoading && (
            <EmptyState
              onCreatePlan={handleCreatePlan}
              onJoinPlan={handleJoinPlan}
            />
          )}

          {/* Plan principal */}
          {primaryPlan && (
            <PrimaryPlanCard plan={primaryPlan} />
          )}

          {/* Lista de planes */}
          {userPlans.length > 1 && (
            <View style={styles.section}>
              <AccessibleText
                variant="subtitle"
                style={[styles.sectionTitle, { color: colors.text }]}
              >
                {t('familyPlan.otherPlans')}
              </AccessibleText>
              
              {userPlans.slice(1).map((plan) => (
                <FamilyPlanCard key={plan.id} plan={plan} />
              ))}
            </View>
          )}

          {/* Acciones rápidas */}
          {hasPlans && (
            <QuickActions
              onCreatePlan={handleCreatePlan}
              onJoinPlan={handleJoinPlan}
            />
          )}
        </ScrollView>

        {/* Formularios modales */}
        {showCreateForm && (
          <CreatePlanModal
            onClose={() => setShowCreateForm(false)}
            onSubmit={createPlan}
            isLoading={isCreating}
            error={createError}
          />
        )}

        {showJoinForm && (
          <JoinPlanModal
            onClose={() => setShowJoinForm(false)}
            onSubmit={joinPlan}
            isLoading={isJoining}
            error={joinError}
          />
        )}
      </View>
    </AuthGuard>
  );
}

// =====================================================
// COMPONENTES AUXILIARES
// =====================================================

interface EmptyStateProps {
  onCreatePlan: () => void;
  onJoinPlan: () => void;
}

function EmptyState({ onCreatePlan, onJoinPlan }: EmptyStateProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t } = useI18n();

  return (
    <ModernCard style={styles.emptyStateCard}>
      <View style={styles.emptyStateContent}>
        <ModernIcon
          name="users"
          size={64}
          color={colors.tint}
          style={styles.emptyStateIcon}
        />
        
        <AccessibleText
          variant="title"
          style={[styles.emptyStateTitle, { color: colors.text }]}
        >
          {t('familyPlan.empty.title')}
        </AccessibleText>
        
        <AccessibleText
          variant="body"
          color="secondary"
          style={styles.emptyStateMessage}
        >
          {t('familyPlan.empty.message')}
        </AccessibleText>

        <View style={styles.emptyStateActions}>
          <PrimaryButton
            onPress={onCreatePlan}
            style={styles.emptyStateButton}
          >
            <View style={styles.buttonContent}>
              <ModernIcon
                name="plus"
                size={20}
                color="white"
                style={styles.buttonIcon}
              />
              <AccessibleText variant="button" style={{ color: 'white' }}>
                {t('familyPlan.actions.create')}
              </AccessibleText>
            </View>
          </PrimaryButton>
          
          <SecondaryButton
            onPress={onJoinPlan}
            style={styles.emptyStateButton}
          >
            <View style={styles.buttonContent}>
              <ModernIcon
                name="user-plus"
                size={20}
                color={colors.tint}
                style={styles.buttonIcon}
              />
              <AccessibleText variant="button" style={{ color: colors.tint }}>
                {t('familyPlan.actions.join')}
              </AccessibleText>
            </View>
          </SecondaryButton>
        </View>
      </View>
    </ModernCard>
  );
}

interface PrimaryPlanCardProps {
  plan: any; // FamilyPlan type
}

function PrimaryPlanCard({ plan }: PrimaryPlanCardProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t } = useI18n();

  return (
    <ModernCard style={styles.primaryPlanCard} gradient>
      <View style={styles.planHeader}>
        <View style={styles.planInfo}>
          <AccessibleText
            variant="subtitle"
            style={[styles.planName, { color: colors.text }]}
          >
            {plan.name}
          </AccessibleText>
          
          <AccessibleText
            variant="bodySmall"
            color="secondary"
            style={styles.planCode}
          >
            {t('familyPlan.code')}: {plan.family_code}
          </AccessibleText>
        </View>

        <View style={styles.planBadge}>
          <AccessibleText
            variant="bodySmall"
            style={[styles.planBadgeText, { color: colors.tint }]}
          >
            {t('familyPlan.primary')}
          </AccessibleText>
        </View>
      </View>

      <View style={styles.planStats}>
        <PlanStat
          icon="users"
          label={t('familyPlan.stats.members')}
          value={plan.member_count}
        />
        <PlanStat
          icon="phone"
          label={t('familyPlan.stats.contacts')}
          value={plan.contact_count}
        />
        <PlanStat
          icon="package"
          label={t('familyPlan.stats.kits')}
          value={plan.kit_count}
        />
        <PlanStat
          icon="map-pin"
          label={t('familyPlan.stats.meetingPoints')}
          value={plan.meeting_point_count}
        />
      </View>

      <PrimaryButton
        style={styles.viewPlanButton}
        onPress={() => {
          // TODO: Navegar a detalles del plan
          Alert.alert('Info', 'Navegación a detalles del plan - Por implementar');
        }}
      >
        {t('familyPlan.actions.viewDetails')}
      </PrimaryButton>
    </ModernCard>
  );
}

interface FamilyPlanCardProps {
  plan: any; // FamilyPlan type
}

function FamilyPlanCard({ plan }: FamilyPlanCardProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t } = useI18n();

  return (
    <ModernCard style={styles.planCard}>
      <View style={styles.planHeader}>
        <View style={styles.planInfo}>
          <AccessibleText
            variant="button"
            style={[styles.planName, { color: colors.text }]}
          >
            {plan.name}
          </AccessibleText>
          
          <AccessibleText
            variant="bodySmall"
            color="secondary"
            style={styles.planCode}
          >
            {t('familyPlan.code')}: {plan.family_code}
          </AccessibleText>
        </View>

        <ModernIcon
          name="chevron-right"
          size={20}
          color={colors.icon}
        />
      </View>

      <View style={styles.planStatsCompact}>
        <AccessibleText
          variant="bodySmall"
          color="secondary"
        >
          {t('familyPlan.stats.membersCount', { count: plan.member_count })}
        </AccessibleText>
      </View>
    </ModernCard>
  );
}

interface PlanStatProps {
  icon: string;
  label: string;
  value: number;
}

function PlanStat({ icon, label, value }: PlanStatProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  return (
    <View style={styles.planStat}>
      <ModernIcon
        name={icon as any}
        size={16}
        color={colors.icon}
        style={styles.planStatIcon}
      />
      <AccessibleText
        variant="bodySmall"
        color="secondary"
        style={styles.planStatLabel}
      >
        {label}
      </AccessibleText>
      <AccessibleText
        variant="button"
        style={[styles.planStatValue, { color: colors.text }]}
      >
        {value}
      </AccessibleText>
    </View>
  );
}

interface QuickActionsProps {
  onCreatePlan: () => void;
  onJoinPlan: () => void;
}

function QuickActions({ onCreatePlan, onJoinPlan }: QuickActionsProps) {
  const { t } = useI18n();

  return (
    <ModernCard style={styles.quickActionsCard}>
      <AccessibleText
        variant="button"
        style={styles.quickActionsTitle}
      >
        {t('familyPlan.quickActions.title')}
      </AccessibleText>

      <View style={styles.quickActionsButtons}>
        <SecondaryButton
          onPress={onCreatePlan}
          style={styles.quickActionButton}
        >
          <View style={styles.buttonContent}>
            <ModernIcon
              name="plus"
              size={16}
              color="currentColor"
              style={styles.buttonIcon}
            />
            <AccessibleText variant="bodySmall">
              {t('familyPlan.quickActions.create')}
            </AccessibleText>
          </View>
        </SecondaryButton>
        
        <SecondaryButton
          onPress={onJoinPlan}
          style={styles.quickActionButton}
        >
          <View style={styles.buttonContent}>
            <ModernIcon
              name="user-plus"
              size={16}
              color="currentColor"
              style={styles.buttonIcon}
            />
            <AccessibleText variant="bodySmall">
              {t('familyPlan.quickActions.join')}
            </AccessibleText>
          </View>
        </SecondaryButton>
      </View>
    </ModernCard>
  );
}

// Componentes modales (por implementar)
function CreatePlanModal({ onClose, onSubmit, isLoading, error }: any) {
  // TODO: Implementar modal de creación
  return null;
}

function JoinPlanModal({ onClose, onSubmit, isLoading, error }: any) {
  // TODO: Implementar modal de unirse
  return null;
}

// =====================================================
// ESTILOS
// =====================================================

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  content: {
    flex: 1,
    padding: Spacing.lg,
  },
  section: {
    marginBottom: Spacing.xl,
  },
  sectionTitle: {
    marginBottom: Spacing.md,
  },
  emptyStateCard: {
    marginTop: Spacing.xl,
  },
  emptyStateContent: {
    alignItems: 'center',
    padding: Spacing.xl,
  },
  emptyStateIcon: {
    marginBottom: Spacing.lg,
  },
  emptyStateTitle: {
    marginBottom: Spacing.md,
    textAlign: 'center',
  },
  emptyStateMessage: {
    marginBottom: Spacing.xl,
    textAlign: 'center',
    lineHeight: 24,
  },
  emptyStateActions: {
    width: '100%',
    gap: Spacing.md,
  },
  emptyStateButton: {
    width: '100%',
  },
  buttonContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  buttonIcon: {
    marginRight: Spacing.sm,
  },
  primaryPlanCard: {
    marginBottom: Spacing.lg,
  },
  planCard: {
    marginBottom: Spacing.md,
  },
  planHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: Spacing.md,
  },
  planInfo: {
    flex: 1,
  },
  planName: {
    marginBottom: Spacing.xs,
  },
  planCode: {
    fontFamily: 'monospace',
  },
  planBadge: {
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 122, 255, 0.1)',
  },
  planBadgeText: {
    fontSize: 12,
    fontWeight: '600',
  },
  planStats: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.lg,
  },
  planStatsCompact: {
    marginBottom: Spacing.sm,
  },
  planStat: {
    alignItems: 'center',
    flex: 1,
  },
  planStatIcon: {
    marginBottom: Spacing.xs,
  },
  planStatLabel: {
    marginBottom: Spacing.xs,
    textAlign: 'center',
    fontSize: 12,
  },
  planStatValue: {
    fontSize: 18,
    fontWeight: '600',
  },
  viewPlanButton: {
    marginTop: Spacing.sm,
  },
  quickActionsCard: {
    marginTop: Spacing.lg,
  },
  quickActionsTitle: {
    marginBottom: Spacing.md,
  },
  quickActionsButtons: {
    flexDirection: 'row',
    gap: Spacing.md,
  },
  quickActionButton: {
    flex: 1,
  },
});
