/**
 * Volcano App - Mapa Interactivo
 * Pantalla del mapa con Volcán <PERSON>, zonas de seguridad y ubicación del usuario
 */

import { InteractiveMap } from '@/components/InteractiveMap';
import { PrimaryButton, SecondaryButton } from '@/components/ui/AccessibleButton';
import { AccessibleText } from '@/components/ui/AccessibleText';
import { ModernHeader } from '@/components/ui/ModernHeader';
import { LanguageSelector } from '@/components/ui/LanguageSelector';
import { useI18n } from '@/hooks/useI18n';
import {
    InfoIcon,
    MapIcon,
    RouteIcon
} from '@/components/ui/ModernIcon';
import { Colors } from '@/constants/Colors';
import { Spacing } from '@/constants/Layout';
import { useColorScheme } from '@/hooks/useColorScheme';
import React, { useState } from 'react';
import {
    Alert,
    StyleSheet,
    View,
} from 'react-native';

export default function MapScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const { t } = useI18n();
  const [userLocation, setUserLocation] = useState<{lat: number; lng: number} | null>(null);
  const [distanceToVolcano, setDistanceToVolcano] = useState<string | null>(null);

  const handleLocationUpdate = (location: { lat: number; lng: number }) => {
    setUserLocation(location);

    // Calcular distancia al volcán
    const volcanoLat = -39.420000;
    const volcanoLng = -71.939167;

    const distance = calculateDistance(
      location.lat, location.lng,
      volcanoLat, volcanoLng
    );

    setDistanceToVolcano(distance.toFixed(2));
  };

  // Funcionalidad de creación de zonas removida para usuarios móviles
  // Los usuarios solo pueden ver zonas oficiales creadas desde el backoffice

  // Función para calcular distancia entre dos puntos (fórmula de Haversine)
  const calculateDistance = (lat1: number, lng1: number, lat2: number, lng2: number): number => {
    const R = 6371; // Radio de la Tierra en km
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLng = (lng2 - lng1) * Math.PI / 180;
    const a =
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
      Math.sin(dLng/2) * Math.sin(dLng/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  };

  const showEvacuationInfo = () => {
    Alert.alert(
      t('evacuation.title'),
      t('evacuation.description') + '\n\n' +
      '🚗 ' + t('evacuation.primaryRoute') + '\n' +
      '🚗 ' + t('evacuation.alternativeRoute') + '\n' +
      '🏠 ' + t('evacuation.shelters') + '\n\n' +
      t('evacuation.fuelTip'),
      [{ text: t('evacuation.understood') }]
    );
  };

  const showSafetyTips = () => {
    Alert.alert(
      t('safety.title'),
      '📍 ' + t('safety.locationMonitored') + '\n\n' +
      '✅ ' + t('safety.stayAway') + '\n' +
      '✅ ' + t('safety.emergencyKit') + '\n' +
      '✅ ' + t('safety.knowRoutes') + '\n' +
      '✅ ' + t('safety.stayInformed') + '\n\n' +
      (distanceToVolcano ? t('safety.distanceToVolcano', { distance: distanceToVolcano }) : ''),
      [{ text: t('common.ok') }]
    );
  };

  const styles = StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: colors.background,
    },

    mapContainer: {
      flex: 1,
    },

    bottomPanel: {
      backgroundColor: colors.background,
      padding: Spacing.md,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },

    infoRow: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginBottom: Spacing.sm,
    },

    buttonRow: {
      flexDirection: 'row',
      gap: Spacing.sm,
    },

    button: {
      flex: 1,
    },

    locationInfo: {
      backgroundColor: colors.backgroundSecondary,
      padding: Spacing.sm,
      borderRadius: 8,
      marginBottom: Spacing.sm,
    },
  });

  return (
    <View style={styles.container}>
      {/* Modern Header with proper safe area */}
      <ModernHeader
        title={t('map.title')}
        subtitle={t('map.subtitle')}
        leftIcon={<MapIcon size={24} color="white" strokeWidth={2.5} />}
        rightIcon={<LanguageSelector compact showLabel={false} />}
        gradient={true}
        gradientColors={colors.gradientMountain}
      />

      {/* Mapa */}
      <View style={styles.mapContainer}>
        <InteractiveMap
          showUserLocation={true}
          showSafetyZones={true}
          onLocationUpdate={handleLocationUpdate}
        />
      </View>

      {/* Panel inferior con información y controles */}
      <View style={styles.bottomPanel}>
        {/* Información de ubicación */}
        {userLocation && (
          <View style={styles.locationInfo}>
            <View style={styles.infoRow}>
              <AccessibleText variant="bodySmall" color="secondary">
                📍 {t('map.yourLocation')}
              </AccessibleText>
              <AccessibleText variant="bodySmall" color="primary">
                {userLocation.lat.toFixed(4)}, {userLocation.lng.toFixed(4)}
              </AccessibleText>
            </View>
            {distanceToVolcano && (
              <View style={styles.infoRow}>
                <AccessibleText variant="bodySmall" color="secondary">
                  🌋 {t('map.distanceToVolcano')}
                </AccessibleText>
                <AccessibleText
                  variant="bodySmall"
                  color={parseFloat(distanceToVolcano) < 10 ? "error" : "primary"}
                >
                  {distanceToVolcano} km
                </AccessibleText>
              </View>
            )}
          </View>
        )}

        {/* Botones de acción */}
        <View style={styles.buttonRow}>
          <PrimaryButton
            style={styles.button}
            onPress={showEvacuationInfo}
            accessibilityLabel={t('accessibility.evacuation')}
            icon={<RouteIcon size={20} color="white" strokeWidth={2.5} />}
            iconPosition="left"
          >
            {t('map.evacuation')}
          </PrimaryButton>

          <SecondaryButton
            style={styles.button}
            onPress={showSafetyTips}
            accessibilityLabel={t('accessibility.tips')}
            icon={<InfoIcon size={20} strokeWidth={2.5} />}
            iconPosition="left"
          >
            {t('map.tips')}
          </SecondaryButton>
        </View>
      </View>
    </View>
  );
}
