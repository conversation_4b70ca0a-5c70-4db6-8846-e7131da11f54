/**
 * 🌋 Volcano App Mobile - Device ID Service
 * Servicio para generar y gestionar identificadores únicos de dispositivo
 */

import Constants from 'expo-constants';
import { Platform } from 'react-native';

// Importación dinámica de AsyncStorage para evitar problemas de resolución
let AsyncStorage: any = null;

// Función para importar AsyncStorage dinámicamente
async function getAsyncStorage() {
  if (!AsyncStorage) {
    try {
      AsyncStorage = (await import('@react-native-async-storage/async-storage')).default;
    } catch (error) {
      console.warn('⚠️ AsyncStorage not available, using memory storage');
      // Fallback a almacenamiento en memoria
      AsyncStorage = {
        getItem: (key: string) => Promise.resolve(memoryStorage[key] || null),
        setItem: (key: string, value: string) => Promise.resolve(memoryStorage[key] = value),
        removeItem: (key: string) => Promise.resolve(delete memoryStorage[key]),
      };
    }
  }
  return AsyncStorage;
}

// Almacenamiento en memoria como fallback
const memoryStorage: { [key: string]: string } = {};

// Clave para almacenar el device_id en AsyncStorage
const DEVICE_ID_KEY = 'volcano_app_device_id';

/**
 * Generar un ID único para el dispositivo
 * Combina información del dispositivo con timestamp y random
 */
function generateDeviceId(): string {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substring(2, 15);
  const platform = Platform.OS;
  const appVersion = Constants.expoConfig?.version || '1.0.0';
  
  // Crear un ID único basado en múltiples factores
  const deviceInfo = `${platform}_${appVersion}_${timestamp}_${random}`;
  
  // Asegurar que tenga entre 10 y 100 caracteres (requisito del backend)
  const deviceId = `volcano_${deviceInfo}`.substring(0, 100);
  
  // Validar longitud mínima
  if (deviceId.length < 10) {
    // Si es muy corto, agregar más caracteres aleatorios
    const extraRandom = Math.random().toString(36).substring(2, 15);
    return `${deviceId}_${extraRandom}`.substring(0, 100);
  }
  
  return deviceId;
}

/**
 * Obtener o generar el device_id del dispositivo
 */
export async function getDeviceId(): Promise<string> {
  try {
    const storage = await getAsyncStorage();

    // Intentar obtener el device_id existente
    let deviceId = await storage.getItem(DEVICE_ID_KEY);

    if (!deviceId) {
      // Si no existe, generar uno nuevo
      deviceId = generateDeviceId();
      await storage.setItem(DEVICE_ID_KEY, deviceId);
      console.log('📱 Generated new device ID:', deviceId);
    } else {
      console.log('📱 Retrieved existing device ID:', deviceId);
    }

    // Validar que cumple con los requisitos del backend
    if (deviceId.length < 10 || deviceId.length > 100) {
      console.warn('⚠️ Device ID length invalid, regenerating...');
      deviceId = generateDeviceId();
      await storage.setItem(DEVICE_ID_KEY, deviceId);
    }

    return deviceId;
  } catch (error) {
    console.error('❌ Error getting device ID:', error);
    // En caso de error, generar un ID temporal
    return generateDeviceId();
  }
}

/**
 * Regenerar el device_id (útil para testing o reset)
 */
export async function regenerateDeviceId(): Promise<string> {
  try {
    const storage = await getAsyncStorage();
    const newDeviceId = generateDeviceId();
    await storage.setItem(DEVICE_ID_KEY, newDeviceId);
    console.log('📱 Regenerated device ID:', newDeviceId);
    return newDeviceId;
  } catch (error) {
    console.error('❌ Error regenerating device ID:', error);
    return generateDeviceId();
  }
}

/**
 * Limpiar el device_id almacenado
 */
export async function clearDeviceId(): Promise<void> {
  try {
    const storage = await getAsyncStorage();
    await storage.removeItem(DEVICE_ID_KEY);
    console.log('📱 Device ID cleared');
  } catch (error) {
    console.error('❌ Error clearing device ID:', error);
  }
}

/**
 * Validar que un device_id cumple con los requisitos
 */
export function validateDeviceId(deviceId: string): boolean {
  return deviceId && deviceId.length >= 10 && deviceId.length <= 100;
}

/**
 * Obtener información del dispositivo para debugging
 */
export function getDeviceInfo() {
  return {
    platform: Platform.OS,
    version: Platform.Version,
    appVersion: Constants.expoConfig?.version || '1.0.0',
    deviceName: Constants.deviceName,
    isDevice: Constants.isDevice,
  };
}

export default {
  getDeviceId,
  regenerateDeviceId,
  clearDeviceId,
  validateDeviceId,
  getDeviceInfo,
};
