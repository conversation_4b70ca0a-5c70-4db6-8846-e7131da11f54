# 🧪 Credenciales de Demostración - VolkanApp Mobile

## Descripción General

Este documento contiene credenciales de usuarios de demostración para testing y desarrollo del sistema de autenticación de VolkanApp Mobile. Estas credenciales están configuradas en el entorno de desarrollo de Supabase.

> **⚠️ IMPORTANTE**: Estas credenciales son solo para desarrollo y testing. **NO** usar en producción.

## 🔐 Usuarios de Demostración

### 1. Usuario Líder Familiar

**Propósito**: Testing de creación y gestión de planes familiares

```
Email: <EMAIL>
Contraseña: Demo123!
Nombre: <PERSON>: +56912345678
Rol: Líder Familiar
```

**Características**:
- Puede crear planes familiares
- Administra miembros del plan
- Gestiona contactos de emergencia
- Configura kits y puntos de encuentro

### 2. Usuario Miembro Familiar

**Propósito**: Testing de unión a planes familiares existentes

```
Email: <EMAIL>
Contraseña: Demo123!
Nombre: <PERSON>: +56987654321
Rol: Miembro Adulto
```

**Características**:
- Se une a planes familiares existentes
- Ve información del plan familiar
- Puede agregar contactos personales
- Acceso de solo lectura a configuraciones

### 3. Usuario Básico

**Propósito**: Testing de funcionalidades básicas sin planes familiares

```
Email: <EMAIL>
Contraseña: Demo123!
Nombre: Ana López
Teléfono: +56955555555
Rol: Usuario Básico
```

**Características**:
- Usuario sin planes familiares
- Testing de estado vacío
- Funcionalidades básicas de la app
- Registro de ubicación

### 4. Usuario de Testing

**Propósito**: Testing automatizado y desarrollo

```
Email: <EMAIL>
Contraseña: Test123!
Nombre: Usuario de Prueba
Teléfono: +56900000000
Rol: Testing
```

**Características**:
- Para tests automatizados
- Datos que pueden ser modificados/eliminados
- Testing de edge cases

## 👨‍👩‍👧‍👦 Plan Familiar de Demostración

### Plan: "Familia García Demo"

```
Código del Plan: DEMO-FAM-001
Líder: María García (<EMAIL>)
Miembros:
  - María García (Líder)
  - Carlos García (Adulto)
  - Sofia García (Niña, 8 años)
  - Roberto García (Adulto Mayor, 70 años)
```

**Datos del Plan**:
- **Contactos de Emergencia**: 3 contactos configurados
- **Kits de Emergencia**: 2 kits (Casa Principal, Auto)
- **Puntos de Encuentro**: 3 puntos (Casa, Plaza, Refugio)
- **Planes de Evacuación**: 2 escenarios configurados

### Contactos de Emergencia Demo

```
1. Dr. Patricio Mendoza
   Teléfono: +56933333333
   Relación: Médico familiar
   Prioridad: 1 (Alta)
   Local: Sí

2. Bomberos Pucón
   Teléfono: +56945555555
   Relación: Servicio de emergencia
   Prioridad: 1 (Alta)
   Local: Sí

3. Tía Carmen (Santiago)
   Teléfono: +56966666666
   Relación: Familiar externo
   Prioridad: 2 (Media)
   Local: No
```

### Kits de Emergencia Demo

#### Kit Casa Principal
```
Ubicación: Closet del pasillo principal
Elementos:
  - Agua: 20 litros (4 personas x 3 días)
  - Comida enlatada: 12 latas
  - Medicamentos: Botiquín básico
  - Linterna: 2 unidades + pilas
  - Radio portátil: 1 unidad
  - Documentos: Copias en bolsa impermeable
```

#### Kit Automóvil
```
Ubicación: Maletero del auto familiar
Elementos:
  - Agua: 4 litros
  - Barras energéticas: 8 unidades
  - Manta térmica: 2 unidades
  - Herramientas básicas: Martillo, alicate
  - Cargador portátil: Para teléfonos
```

### Puntos de Encuentro Demo

```
1. Casa Familiar (Primario)
   Dirección: Av. Bernardo O'Higgins 123, Pucón
   Coordenadas: -39.2904, -71.9048
   Accesibilidad: Rampa para silla de ruedas
   
2. Plaza de Armas Pucón (Secundario)
   Dirección: Plaza de Armas, Centro, Pucón
   Coordenadas: -39.2823, -71.9520
   Servicios: Baños públicos, iluminación
   
3. Refugio Municipal (Evacuación)
   Dirección: Gimnasio Municipal, Pucón
   Coordenadas: -39.2756, -71.9612
   Capacidad: 500 personas
```

## 🚀 Instrucciones de Uso

### Para Desarrolladores

#### 1. Testing de Login

```typescript
// Ejemplo de test automatizado
const testCredentials = {
  email: '<EMAIL>',
  password: 'Demo123!'
};

await authService.login(testCredentials);
```

#### 2. Testing de Registro

```typescript
// Para testing, usar emails únicos
const testUser = {
  email: `test.${Date.now()}@volcanoapp.com`,
  password: 'Test123!',
  name: 'Usuario Test',
  phone: '+56900000000'
};

await authService.register(testUser);
```

#### 3. Testing de Planes Familiares

```typescript
// Unirse al plan demo
const joinData = {
  family_code: 'DEMO-FAM-001',
  user_info: {
    name: 'Nuevo Miembro',
    phone: '+56911111111',
    role: 'ADULT'
  }
};

await familyPlanService.joinFamilyPlan(joinData);
```

### Para Testing Manual

#### Flujo Completo de Testing

1. **Registro de Usuario Nuevo**
   - Usar email único: `test.{timestamp}@volcanoapp.com`
   - Contraseña: `Test123!`
   - Completar perfil

2. **Login con Usuario Demo**
   - Usar credenciales de `<EMAIL>`
   - Verificar carga de datos del plan familiar

3. **Unirse a Plan Familiar**
   - Crear usuario nuevo
   - Usar código: `DEMO-FAM-001`
   - Verificar sincronización de datos

4. **Testing de Funcionalidades**
   - Editar perfil de usuario
   - Agregar contactos de emergencia
   - Crear nuevos kits
   - Configurar puntos de encuentro

## 🔧 Configuración de Entorno

### Variables de Entorno para Testing

```bash
# .env.test
EXPO_PUBLIC_SUPABASE_URL=https://gdcbnnlmxwazgnwpfelt.supabase.co
EXPO_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
EXPO_PUBLIC_API_BASE_URL=http://localhost:3001

# Testing flags
EXPO_PUBLIC_ENABLE_DEMO_MODE=true
EXPO_PUBLIC_SKIP_AUTH_VALIDATION=false
```

### Configuración de Supabase para Demo

#### Políticas de Seguridad (RLS)

```sql
-- Permitir acceso a usuarios demo
CREATE POLICY "Demo users can access demo data" ON user_profiles
FOR ALL USING (
  email LIKE '%@volcanoapp.com' OR 
  email LIKE '<EMAIL>' OR
  email LIKE '<EMAIL>'
);
```

#### Datos de Seed

```sql
-- Insertar usuarios demo (ejecutar en Supabase SQL Editor)
INSERT INTO user_profiles (id, email, name, phone, created_at, updated_at)
VALUES 
  ('demo-leader-uuid', '<EMAIL>', 'María García', '+56912345678', NOW(), NOW()),
  ('demo-member-uuid', '<EMAIL>', 'Carlos García', '+56987654321', NOW(), NOW()),
  ('demo-basic-uuid', '<EMAIL>', 'Ana López', '+56955555555', NOW(), NOW());
```

## 🧪 Scripts de Testing

### Crear Usuario de Prueba

```bash
# Script para crear usuario de testing rápido
curl -X POST http://localhost:3001/api/mobile/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "test.'$(date +%s)'@volcanoapp.com",
    "password": "Test123!",
    "name": "Usuario Test",
    "phone": "+56900000000"
  }'
```

### Verificar Estado de Demo

```bash
# Verificar que los usuarios demo existen
curl -X GET http://localhost:3001/api/mobile/family-plans/user \
  -H "Authorization: Bearer <demo-user-token>"
```

### Limpiar Datos de Testing

```bash
# Limpiar usuarios de testing (mantener demos)
curl -X DELETE http://localhost:3001/api/mobile/testing/cleanup \
  -H "Authorization: Bearer <admin-token>"
```

## ⚠️ Consideraciones de Seguridad

### Datos Sensibles

- **NO** usar datos reales en usuarios demo
- **NO** usar números de teléfono reales
- **NO** usar direcciones reales específicas
- Usar solo datos ficticios y genéricos

### Limitaciones

- Los usuarios demo tienen acceso limitado
- No pueden eliminar el plan familiar principal
- No pueden modificar datos críticos del sistema
- Sesiones expiran cada 24 horas

### Limpieza Periódica

```sql
-- Limpiar usuarios de testing antiguos (ejecutar semanalmente)
DELETE FROM user_profiles 
WHERE email LIKE '<EMAIL>' 
AND created_at < NOW() - INTERVAL '7 days';
```

## 📞 Soporte para Testing

### Problemas Comunes

1. **Usuario demo no puede hacer login**
   - Verificar que Supabase Auth esté habilitado
   - Confirmar que el usuario existe en auth.users

2. **Plan familiar no se carga**
   - Verificar políticas RLS en Supabase
   - Confirmar que el backend está ejecutándose

3. **Datos no se sincronizan**
   - Verificar conexión a internet
   - Revisar logs del backend

### Contacto

Para problemas con usuarios demo:
- Revisar logs en Supabase Dashboard
- Verificar estado del backend en `http://localhost:3001/api/mobile/_health`
- Consultar documentación en `docs/AUTHENTICATION.md`

---

## 🔄 Actualización de Credenciales

**Última actualización**: 3 de enero de 2025

**Próxima revisión**: 3 de febrero de 2025

> **Nota**: Las credenciales demo se actualizan mensualmente por seguridad. Verificar este documento antes de usar en testing.

---

*Estas credenciales son exclusivamente para desarrollo y testing. Mantener la confidencialidad y no compartir fuera del equipo de desarrollo.*
