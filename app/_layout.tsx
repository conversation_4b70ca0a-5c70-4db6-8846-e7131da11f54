import { DarkTheme, DefaultTheme, Theme<PERSON>rovider } from '@react-navigation/native';
import { useFonts } from 'expo-font';
import { Stack } from 'expo-router';
import { StatusBar } from 'expo-status-bar';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import 'react-native-reanimated';

import { useColorScheme } from '@/hooks/useColorScheme';
import { QueryProvider } from '@/providers/QueryProvider';
import { AuthProvider } from '@/providers/AuthProvider';
import { AuthNavigator } from '@/components/AuthNavigator';
import { useEffect } from 'react';
// Initialize i18n
import '@/i18n';

export default function RootLayout() {
  const colorScheme = useColorScheme();
  const [loaded] = useFonts({
    SpaceMono: require('../assets/fonts/SpaceMono-Regular.ttf'),
  });

  // Inicializar device_id al cargar la aplicación
  useEffect(() => {
    const initializeDeviceId = async () => {
      try {
        const { getDeviceId } = await import('@/services/deviceId');
        const deviceId = await getDeviceId();
        console.log('📱 Device ID initialized:', deviceId);
      } catch (error) {
        console.error('❌ Failed to initialize device ID:', error);
      }
    };

    initializeDeviceId();
  }, []);

  if (!loaded) {
    // Async font loading only occurs in development.
    return null;
  }

  return (
    <SafeAreaProvider>
      <AuthProvider>
        <QueryProvider>
          <AuthNavigator>
            <ThemeProvider value={colorScheme === 'dark' ? DarkTheme : DefaultTheme}>
              <Stack>
                <Stack.Screen name="(tabs)" options={{ headerShown: false }} />
                <Stack.Screen name="(auth)" options={{ headerShown: false }} />
                <Stack.Screen name="+not-found" />
              </Stack>
              <StatusBar style="auto" />
            </ThemeProvider>
          </AuthNavigator>
        </QueryProvider>
      </AuthProvider>
    </SafeAreaProvider>
  );
}
