/**
 * 🌋 Volcano App Mobile - Family Plan Service
 * Servicio para gestión de planes familiares con autenticación
 */

import { getAPIBaseURL } from './network-config';
import { getAuthToken, getCurrentUserId } from './auth';

// =====================================================
// TIPOS
// =====================================================

export interface FamilyMember {
  id: string;
  name: string;
  role: 'LEADER' | 'ADULT' | 'CHILD' | 'ELDERLY' | 'DISABLED';
  phone?: string;
  medical_info?: {
    blood_type?: string;
    allergies?: string[];
    medications?: string[];
  };
  special_needs?: {
    mobility?: string;
    assistance_needed?: boolean;
  };
}

export interface EmergencyContact {
  id: string;
  name: string;
  phone: string;
  relationship: string;
  priority: number;
  is_local: boolean;
  notes?: string;
}

export interface EmergencyKit {
  id: string;
  name: string;
  location: string;
  description?: string;
  last_checked?: string;
  total_items: number;
  checked_items: number;
  critical_items: number;
  expiring_soon: number;
}

export interface EmergencyKitItem {
  id: string;
  kit_id: string;
  category: 'FOOD' | 'WATER' | 'MEDICAL' | 'TOOLS' | 'CLOTHING' | 'DOCUMENTS' | 'COMMUNICATION' | 'OTHER';
  item_name: string;
  quantity: number;
  unit: string;
  expiry_date?: string;
  priority: 'CRITICAL' | 'IMPORTANT' | 'OPTIONAL';
  is_checked: boolean;
  notes?: string;
}

export interface MeetingPoint {
  id: string;
  name: string;
  description?: string;
  address: string;
  coordinates: {
    lat: number;
    lng: number;
  };
  point_type: 'PRIMARY' | 'SECONDARY' | 'EVACUATION' | 'SHELTER';
  accessibility_info?: {
    wheelchair_accessible?: boolean;
    parking_available?: boolean;
    lighting?: string;
  };
  contact_info?: {
    emergency_phone?: string;
    nearby_services?: string[];
  };
}

export interface EvacuationPlan {
  id: string;
  scenario: 'NORMAL' | 'ADVISORY' | 'WATCH' | 'WARNING' | 'EMERGENCY';
  plan_name: string;
  description: string;
  primary_route: {
    description: string;
    route?: string;
    distance?: string;
    time?: string;
  };
  alternative_routes?: Array<{
    description: string;
    time?: string;
  }>;
  transportation?: {
    primary?: string;
    backup?: string;
  };
  special_instructions?: string;
  estimated_time: number;
}

export interface FamilyPlan {
  id: string;
  name: string;
  description?: string;
  family_code: string;
  created_at: string;
  member_count: number;
  contact_count: number;
  kit_count: number;
  meeting_point_count: number;
  evacuation_plan_count: number;
  sync_version: number;
  members?: FamilyMember[];
  contacts?: EmergencyContact[];
  kits?: EmergencyKit[];
  meeting_points?: MeetingPoint[];
  evacuation_plans?: EvacuationPlan[];
}

export interface CreateFamilyPlanData {
  name: string;
  description?: string;
  user_info: {
    name: string;
    phone?: string;
    role: 'LEADER';
  };
}

export interface JoinFamilyPlanData {
  family_code: string;
  user_info: {
    name: string;
    phone?: string;
    role: 'ADULT' | 'CHILD' | 'ELDERLY' | 'DISABLED';
    medical_info?: FamilyMember['medical_info'];
    special_needs?: FamilyMember['special_needs'];
  };
}

// =====================================================
// CONFIGURACIÓN
// =====================================================

const API_BASE_URL = getAPIBaseURL();
const FAMILY_PLAN_ENDPOINT = `${API_BASE_URL}/api/mobile/family-plans`;

// =====================================================
// SERVICIO
// =====================================================

class FamilyPlanService {
  /**
   * Obtener headers de autenticación
   */
  private async getHeaders(): Promise<Record<string, string>> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
    };

    const token = await getAuthToken();
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    const userId = getCurrentUserId();
    if (userId) {
      headers['X-User-ID'] = userId;
    }

    return headers;
  }

  /**
   * Crear un nuevo plan familiar
   */
  async createFamilyPlan(data: CreateFamilyPlanData): Promise<{
    success: boolean;
    data?: {
      id: string;
      name: string;
      family_code: string;
      created_at: string;
      member_count: number;
      sync_version: number;
    };
    error?: string;
  }> {
    try {
      console.log('👨‍👩‍👧‍👦 Creating family plan:', data.name);

      const response = await fetch(FAMILY_PLAN_ENDPOINT, {
        method: 'POST',
        headers: await this.getHeaders(),
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('❌ Error creating family plan:', result);
        return {
          success: false,
          error: result.message || 'Error al crear plan familiar',
        };
      }

      console.log('✅ Family plan created successfully');
      return {
        success: true,
        data: result.data,
      };
    } catch (error) {
      console.error('❌ Network error creating family plan:', error);
      return {
        success: false,
        error: 'Error de red al crear plan familiar',
      };
    }
  }

  /**
   * Unirse a un plan familiar existente
   */
  async joinFamilyPlan(data: JoinFamilyPlanData): Promise<{
    success: boolean;
    data?: {
      family_plan: {
        id: string;
        name: string;
        member_count: number;
      };
      member_id: string;
    };
    error?: string;
  }> {
    try {
      console.log('👨‍👩‍👧‍👦 Joining family plan with code:', data.family_code);

      const response = await fetch(`${FAMILY_PLAN_ENDPOINT}/join`, {
        method: 'POST',
        headers: await this.getHeaders(),
        body: JSON.stringify(data),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('❌ Error joining family plan:', result);
        return {
          success: false,
          error: result.message || 'Error al unirse al plan familiar',
        };
      }

      console.log('✅ Joined family plan successfully');
      return {
        success: true,
        data: result.data,
      };
    } catch (error) {
      console.error('❌ Network error joining family plan:', error);
      return {
        success: false,
        error: 'Error de red al unirse al plan familiar',
      };
    }
  }

  /**
   * Obtener plan familiar completo
   */
  async getFamilyPlan(planId: string, include?: string[]): Promise<{
    success: boolean;
    data?: FamilyPlan;
    error?: string;
  }> {
    try {
      console.log('👨‍👩‍👧‍👦 Getting family plan:', planId);

      const includeParam = include ? `?include=${include.join(',')}` : '';
      const response = await fetch(`${FAMILY_PLAN_ENDPOINT}/${planId}${includeParam}`, {
        method: 'GET',
        headers: await this.getHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('❌ Error getting family plan:', result);
        return {
          success: false,
          error: result.message || 'Error al obtener plan familiar',
        };
      }

      console.log('✅ Family plan retrieved successfully');
      return {
        success: true,
        data: result.data.plan,
      };
    } catch (error) {
      console.error('❌ Network error getting family plan:', error);
      return {
        success: false,
        error: 'Error de red al obtener plan familiar',
      };
    }
  }

  /**
   * Obtener planes familiares del usuario
   */
  async getUserFamilyPlans(): Promise<{
    success: boolean;
    data?: FamilyPlan[];
    error?: string;
  }> {
    try {
      console.log('👨‍👩‍👧‍👦 Getting user family plans');

      const response = await fetch(`${FAMILY_PLAN_ENDPOINT}/user`, {
        method: 'GET',
        headers: await this.getHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('❌ Error getting user family plans:', result);
        return {
          success: false,
          error: result.message || 'Error al obtener planes familiares',
        };
      }

      console.log('✅ User family plans retrieved successfully');
      return {
        success: true,
        data: result.data,
      };
    } catch (error) {
      console.error('❌ Network error getting user family plans:', error);
      return {
        success: false,
        error: 'Error de red al obtener planes familiares',
      };
    }
  }

  /**
   * Agregar contacto de emergencia
   */
  async addEmergencyContact(planId: string, contact: Omit<EmergencyContact, 'id'>): Promise<{
    success: boolean;
    data?: EmergencyContact;
    error?: string;
  }> {
    try {
      console.log('👨‍👩‍👧‍👦 Adding emergency contact to plan:', planId);

      const response = await fetch(`${FAMILY_PLAN_ENDPOINT}/${planId}/contacts`, {
        method: 'POST',
        headers: await this.getHeaders(),
        body: JSON.stringify(contact),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('❌ Error adding emergency contact:', result);
        return {
          success: false,
          error: result.message || 'Error al agregar contacto de emergencia',
        };
      }

      console.log('✅ Emergency contact added successfully');
      return {
        success: true,
        data: result.data,
      };
    } catch (error) {
      console.error('❌ Network error adding emergency contact:', error);
      return {
        success: false,
        error: 'Error de red al agregar contacto de emergencia',
      };
    }
  }

  /**
   * Agregar kit de emergencia
   */
  async addEmergencyKit(planId: string, kit: Omit<EmergencyKit, 'id' | 'total_items' | 'checked_items' | 'critical_items' | 'expiring_soon'>): Promise<{
    success: boolean;
    data?: EmergencyKit;
    error?: string;
  }> {
    try {
      console.log('👨‍👩‍👧‍👦 Adding emergency kit to plan:', planId);

      const response = await fetch(`${FAMILY_PLAN_ENDPOINT}/${planId}/kits`, {
        method: 'POST',
        headers: await this.getHeaders(),
        body: JSON.stringify(kit),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('❌ Error adding emergency kit:', result);
        return {
          success: false,
          error: result.message || 'Error al agregar kit de emergencia',
        };
      }

      console.log('✅ Emergency kit added successfully');
      return {
        success: true,
        data: result.data,
      };
    } catch (error) {
      console.error('❌ Network error adding emergency kit:', error);
      return {
        success: false,
        error: 'Error de red al agregar kit de emergencia',
      };
    }
  }

  /**
   * Agregar punto de encuentro
   */
  async addMeetingPoint(planId: string, point: Omit<MeetingPoint, 'id'>): Promise<{
    success: boolean;
    data?: MeetingPoint;
    error?: string;
  }> {
    try {
      console.log('👨‍👩‍👧‍👦 Adding meeting point to plan:', planId);

      const response = await fetch(`${FAMILY_PLAN_ENDPOINT}/${planId}/meeting-points`, {
        method: 'POST',
        headers: await this.getHeaders(),
        body: JSON.stringify(point),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('❌ Error adding meeting point:', result);
        return {
          success: false,
          error: result.message || 'Error al agregar punto de encuentro',
        };
      }

      console.log('✅ Meeting point added successfully');
      return {
        success: true,
        data: result.data,
      };
    } catch (error) {
      console.error('❌ Network error adding meeting point:', error);
      return {
        success: false,
        error: 'Error de red al agregar punto de encuentro',
      };
    }
  }

  /**
   * Obtener plan de evacuación actual
   */
  async getCurrentEvacuationPlan(planId: string, alertLevel: string): Promise<{
    success: boolean;
    data?: EvacuationPlan;
    error?: string;
  }> {
    try {
      console.log('👨‍👩‍👧‍👦 Getting current evacuation plan for alert level:', alertLevel);

      const response = await fetch(`${FAMILY_PLAN_ENDPOINT}/${planId}/evacuation-plans/current?alert_level=${alertLevel}`, {
        method: 'GET',
        headers: await this.getHeaders(),
      });

      const result = await response.json();

      if (!response.ok) {
        console.error('❌ Error getting evacuation plan:', result);
        return {
          success: false,
          error: result.message || 'Error al obtener plan de evacuación',
        };
      }

      console.log('✅ Evacuation plan retrieved successfully');
      return {
        success: true,
        data: result.data,
      };
    } catch (error) {
      console.error('❌ Network error getting evacuation plan:', error);
      return {
        success: false,
        error: 'Error de red al obtener plan de evacuación',
      };
    }
  }
}

// =====================================================
// INSTANCIA SINGLETON
// =====================================================

export const familyPlanService = new FamilyPlanService();

// =====================================================
// FUNCIONES DE UTILIDAD
// =====================================================

/**
 * Verificar si el usuario tiene planes familiares
 */
export async function hasUserFamilyPlans(): Promise<boolean> {
  try {
    const result = await familyPlanService.getUserFamilyPlans();
    return result.success && (result.data?.length || 0) > 0;
  } catch (error) {
    console.error('❌ Error checking user family plans:', error);
    return false;
  }
}

/**
 * Obtener el primer plan familiar del usuario
 */
export async function getUserPrimaryFamilyPlan(): Promise<FamilyPlan | null> {
  try {
    const result = await familyPlanService.getUserFamilyPlans();
    if (result.success && result.data && result.data.length > 0) {
      return result.data[0];
    }
    return null;
  } catch (error) {
    console.error('❌ Error getting primary family plan:', error);
    return null;
  }
}
