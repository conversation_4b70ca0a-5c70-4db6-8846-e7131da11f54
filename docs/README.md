# 📚 Documentación VolkanApp

Documentación técnica completa para el desarrollo y mantenimiento de VolkanApp.

## 📖 Índice de Documentación

### 🔐 Sistema de Autenticación
- **[🔐 AUTHENTICATION.md](./AUTHENTICATION.md)** - Arquitectura completa del sistema de autenticación
- **[👤 USER_GUIDE.md](./USER_GUIDE.md)** - Guía de usuario para registro y gestión de cuenta
- **[🛠️ DEVELOPMENT_AUTH.md](./DEVELOPMENT_AUTH.md)** - Guía de desarrollo con hooks y componentes
- **[🧪 DEMO_CREDENTIALS.md](./DEMO_CREDENTIALS.md)** - Credenciales de demostración para testing

### 👨‍👩‍👧‍👦 Planes Familiares
- **[📄 FAMILY_PLANS_API.md](./FAMILY_PLANS_API.md)** - Documentación completa de la API
- **[🚀 FAMILY_PLANS_SETUP.md](./FAMILY_PLANS_SETUP.md)** - Configuración e instalación

### 🌍 Sistema Multiidioma
- **[📄 MULTILINGUAL-SYSTEM.md](./MULTILINGUAL-SYSTEM.md)** - Documentación completa del sistema i18n
- **[🚀 I18N-QUICK-START.md](./I18N-QUICK-START.md)** - Guía rápida para desarrolladores

## 🎯 Propósito

Esta documentación cubre los sistemas principales de VolkanApp Mobile, diseñado para garantizar seguridad y accesibilidad a poblaciones vulnerables en zonas de riesgo volcánico:

- **Sistema de Autenticación**: Gestión segura de usuarios y planes familiares
- **Planes Familiares**: Coordinación de emergencias volcánicas a nivel familiar
- **Sistema Multiidioma**: Accesibilidad para poblaciones diversas:
  - **22% adultos con discapacidades**
  - **13% población mayor**
  - **30% migrantes haitianos**

## 🌐 Idiomas Soportados

| Idioma | Código | Estado | Población Objetivo |
|--------|--------|--------|-------------------|
| Español | `es` | ✅ 100% | Población local chilena |
| English | `en` | ✅ 100% | Turistas y expatriados |
| Kreyòl Ayisyen | `ht` | ✅ 100% | Comunidad haitiana |

## 🔧 Para Desarrolladores

### Inicio Rápido
```typescript
import { useI18n } from '@/hooks/useI18n';

export default function MyComponent() {
  const { t } = useI18n();
  return <AccessibleText>{t('home.title')}</AccessibleText>;
}
```

### Estructura de Archivos
```
docs/
├── README.md                    # Este archivo
├── AUTHENTICATION.md            # Sistema de autenticación
├── USER_GUIDE.md               # Guía de usuario
├── DEVELOPMENT_AUTH.md         # Guía de desarrollo auth
├── DEMO_CREDENTIALS.md         # Credenciales de testing
├── FAMILY_PLANS_API.md         # API de planes familiares
├── FAMILY_PLANS_SETUP.md       # Setup de planes familiares
├── MULTILINGUAL-SYSTEM.md      # Sistema multiidioma
└── I18N-QUICK-START.md         # Guía rápida i18n

services/
├── auth.ts                     # Servicio de autenticación
├── familyPlan.ts              # Servicio de planes familiares
└── supabase.ts                # Cliente de Supabase

components/
├── AuthGuard.tsx              # Protección de rutas
├── UserProfile.tsx            # Perfil de usuario
└── UserHeader.tsx             # Header con usuario

i18n/
├── index.ts                   # Configuración principal
└── translations/
    ├── es.json               # Español (base)
    ├── en.json               # English
    └── ht.json               # Kreyòl Ayisyen
```

## 📱 Funcionalidades Implementadas

### 🔐 Autenticación
- ✅ **Registro de usuarios** - Creación de cuentas con Supabase
- ✅ **Inicio de sesión** - Autenticación segura con JWT
- ✅ **Gestión de perfil** - Edición de datos personales
- ✅ **Protección de rutas** - Acceso controlado a funciones

### 👨‍👩‍👧‍👦 Planes Familiares
- ✅ **Creación de planes** - Organización familiar para emergencias
- ✅ **Unión a planes** - Código familiar para miembros
- ✅ **Contactos de emergencia** - Lista de contactos críticos
- ✅ **Kits de emergencia** - Inventario de suministros
- ✅ **Puntos de encuentro** - Ubicaciones de reunión familiar

### 📱 Pantallas Principales
- ✅ **Home** - Dashboard principal con estado volcánico
- ✅ **Explore** - Mapa interactivo y rutas de evacuación
- ✅ **Precursors** - Análisis de precursores volcánicos
- ✅ **Family Plan** - Gestión de planes familiares
- ✅ **Navigation** - Navegación de tabs

## ♿ Cumplimiento de Accesibilidad

- **WCAG 2.1 AA** - Estándares de accesibilidad web
- **Screen Readers** - Soporte completo para lectores de pantalla
- **Voice Navigation** - Navegación por voz en todos los idiomas
- **Cultural Adaptation** - Adaptación cultural para cada población

## 🚀 Estado del Proyecto

**✅ Sistemas Principales Implementados**:
- **Autenticación**: 100% funcional con Supabase
- **Planes Familiares**: 100% funcional con backend integrado
- **Multiidioma**: 100% funcional (ES, EN, HT)
- **UI/UX**: Diseño moderno y accesible preservado

**🎯 Listo para Producción** - Todos los sistemas críticos implementados y documentados.

## 📞 Contacto

Para consultas sobre la documentación, sistemas implementados o desarrollo:
- **Autenticación**: Revisar `AUTHENTICATION.md` y `DEVELOPMENT_AUTH.md`
- **Planes Familiares**: Consultar `FAMILY_PLANS_API.md`
- **Testing**: Usar credenciales en `DEMO_CREDENTIALS.md`
- **Multiidioma**: Ver `MULTILINGUAL-SYSTEM.md`

Contactar al equipo de desarrollo VolkanApp para soporte técnico.

---

*Última actualización: 3 de enero de 2025*