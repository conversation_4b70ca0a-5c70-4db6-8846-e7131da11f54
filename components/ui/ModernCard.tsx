/**
 * Modern Card Component
 * Elevated card with modern shadows and styling
 * Inspired by travel app designs
 */

import React from 'react';
import {
  View,
  StyleSheet,
  Pressable,
  ViewStyle,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors } from '@/constants/Colors';
import { BorderRadius, Spacing } from '@/constants/Layout';
import { useColorScheme } from '@/hooks/useColorScheme';

interface ModernCardProps {
  children: React.ReactNode;
  onPress?: () => void;
  style?: ViewStyle;
  gradient?: boolean;
  gradientColors?: string[];
  elevation?: 'low' | 'medium' | 'high';
  padding?: 'sm' | 'md' | 'lg';
}

export function ModernCard({
  children,
  onPress,
  style,
  gradient = false,
  gradientColors,
  elevation = 'medium',
  padding = 'md',
}: ModernCardProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const elevationStyles = {
    low: {
      ...Platform.select({
        ios: {
          shadowColor: colors.shadow,
          shadowOffset: { width: 0, height: 2 },
          shadowOpacity: 0.1,
          shadowRadius: 4,
        },
        android: {
          elevation: 2,
        },
      }),
    },
    medium: {
      ...Platform.select({
        ios: {
          shadowColor: colors.cardShadow,
          shadowOffset: { width: 0, height: 4 },
          shadowOpacity: 0.15,
          shadowRadius: 8,
        },
        android: {
          elevation: 4,
        },
      }),
    },
    high: {
      ...Platform.select({
        ios: {
          shadowColor: colors.shadowStrong,
          shadowOffset: { width: 0, height: 8 },
          shadowOpacity: 0.2,
          shadowRadius: 16,
        },
        android: {
          elevation: 8,
        },
      }),
    },
  };

  const paddingStyles = {
    sm: Spacing.sm,
    md: Spacing.md,
    lg: Spacing.lg,
  };

  const cardStyle = [
    styles.container,
    {
      backgroundColor: colors.background,
      padding: paddingStyles[padding],
    },
    elevationStyles[elevation],
    style,
  ];

  const CardContent = () => (
    <View style={cardStyle}>
      {gradient && gradientColors ? (
        <LinearGradient
          colors={gradientColors}
          style={StyleSheet.absoluteFillObject}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        />
      ) : null}
      <View style={gradient ? styles.gradientContent : undefined}>
        {children}
      </View>
    </View>
  );

  if (onPress) {
    return (
      <Pressable
        onPress={onPress}
        style={({ pressed }) => [
          pressed && styles.pressed,
        ]}
        accessibilityRole="button"
      >
        <CardContent />
      </Pressable>
    );
  }

  return <CardContent />;
}

const styles = StyleSheet.create({
  container: {
    borderRadius: BorderRadius.lg,
    marginBottom: Spacing.md,
    overflow: 'hidden',
  },

  gradientContent: {
    position: 'relative',
    zIndex: 1,
  },

  pressed: {
    opacity: 0.95,
    transform: [{ scale: 0.98 }],
  },
});