/**
 * useI18n Hook
 * Custom hook que integra react-i18next con VolkanApp
 * Proporciona funciones de traducción y gestión de idioma
 */

import { useTranslation } from 'react-i18next';
import { useCallback } from 'react';
import { changeLanguage, getCurrentLanguage, getSupportedLanguages } from '@/i18n';

export function useI18n() {
  const { t, i18n } = useTranslation();
  
  // Función de traducción con soporte para interpolación
  const translate = useCallback((key: string, options?: any) => {
    return t(key, options);
  }, [t]);

  // Cambiar idioma
  const setLanguage = useCallback(async (languageCode: string) => {
    await changeLanguage(languageCode);
  }, []);

  // Obtener idioma actual
  const currentLanguage = getCurrentLanguage();

  // Obtener idiomas soportados
  const supportedLanguages = getSupportedLanguages();

  // Detectar si es RTL (para futuro soporte)
  const isRTL = i18n.dir() === 'rtl';

  return {
    t: translate,
    setLanguage,
    currentLanguage,
    supportedLanguages,
    isRTL,
    // Funciones de conveniencia para usar en componentes
    ready: i18n.isInitialized,
  };
}

// Hook especializado para AccessibleText
export function useAccessibleText() {
  const { t } = useI18n();

  // Función que prepara texto para AccessibleText con interpolación
  const getText = useCallback((key: string, options?: any) => {
    const text = t(key, options);
    
    // Procesar interpolación de bold para AccessibleText
    if (typeof text === 'string' && text.includes('{{bold}}')) {
      // Para AccessibleText, removemos las etiquetas bold ya que 
      // el formato se maneja en el estilo del componente
      return text
        .replace(/\{\{bold\}\}/g, '')
        .replace(/\{\{\/bold\}\}/g, '');
    }
    
    return text;
  }, [t]);

  return { getText, t };
}