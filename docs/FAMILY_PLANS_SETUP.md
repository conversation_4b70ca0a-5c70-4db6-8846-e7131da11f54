# 🚀 Configuración del Backend de Planes Familiares

## 📋 Resumen de Implementación

Se ha implementado un backend completo para el sistema de **Planes Familiares de Emergencias** del punto 4 del PRD-VOLKANAPP. El sistema incluye:

### ✅ Funcionalidades Implementadas

1. **Gestión de Planes Familiares**
   - Crear planes familiares con códigos únicos
   - Unirse a planes existentes con código
   - Gestión de miembros con roles específicos

2. **Contactos de Emergencia**
   - Lista priorizada de contactos
   - Información completa (teléfono, email, dirección)
   - Clasificación por tipo (local/externo)

3. **Kits de Emergencia**
   - Múltiples kits por familia
   - Items categorizados con vencimientos
   - Sistema de verificación periódica

4. **Puntos de Encuentro**
   - Puntos georreferenciados
   - Tipos: primario, secundario, evacuación, refugio
   - Información de accesibilidad

5. **Planes de Evacuación**
   - Planes por escenario de alerta
   - Rutas primarias y alternativas
   - Instrucciones específicas

6. **Sincronización Offline**
   - Soporte completo offline-first
   - Resolución automática de conflictos
   - Sincronización por timestamp

### 🏗️ Arquitectura Implementada

```
📁 backoffice/backend/src/
├── 📁 types/
│   └── index.ts                    # Tipos TypeScript agregados
├── 📁 middleware/
│   ├── auth.ts                     # Middleware de autenticación móvil
│   └── validation.ts               # Validaciones para planes familiares
├── 📁 services/
│   ├── familyPlanService.ts        # Lógica de negocio principal
│   └── syncService.ts              # Servicio de sincronización
├── 📁 controllers/
│   ├── familyPlans.ts              # Controladores principales
│   └── familySync.ts               # Controladores de sincronización
├── 📁 routes/
│   ├── familyPlans.ts              # Rutas de planes familiares
│   └── mobile.ts                   # Rutas móviles actualizadas
└── 📁 tests/
    └── controllers/
        └── familyPlans.test.ts     # Tests unitarios

📁 backoffice/database/migrations/
├── family_plans_schema.sql         # Esquema completo de BD
└── family_plans_seed.sql           # Datos de ejemplo

📁 docs/
├── FAMILY_PLANS_API.md             # Documentación completa para desarrolladores
└── FAMILY_PLANS_SETUP.md           # Este archivo
```

## 🛠️ Instalación y Configuración

### 1. Ejecutar Migraciones de Base de Datos

```bash
# Conectar a Supabase y ejecutar el esquema
psql -h <supabase-host> -U postgres -d postgres -f backoffice/database/migrations/family_plans_schema.sql

# Insertar datos de ejemplo (opcional)
psql -h <supabase-host> -U postgres -d postgres -f backoffice/database/migrations/family_plans_seed.sql
```

### 2. Verificar Configuración del Backend

El backend ya está configurado para usar las nuevas funcionalidades. Verificar que las siguientes dependencias estén instaladas:

```bash
cd backoffice/backend
npm install
```

### 3. Variables de Entorno

Asegurar que estas variables estén configuradas en `.env`:

```bash
# Supabase (ya configurado)
SUPABASE_URL=your_supabase_url
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# Configuración de planes familiares
FAMILY_PLAN_MAX_MEMBERS=20
FAMILY_PLAN_MAX_KITS=10
FAMILY_PLAN_MAX_CONTACTS=50
FAMILY_PLAN_CODE_EXPIRY_DAYS=30
FAMILY_PLAN_SYNC_RETENTION_DAYS=30
```

### 4. Iniciar el Servidor

```bash
cd backoffice/backend
npm run dev
```

### 5. Verificar Funcionamiento

```bash
# Verificar que las rutas estén disponibles
curl http://localhost:3001/api/mobile/family-plans/_info

# Verificar estado del servidor
curl http://localhost:3001/api/mobile/_health
```

## 🧪 Testing

### Ejecutar Tests

```bash
cd backoffice/backend
npm test -- --testPathPattern=familyPlans
```

### Tests Manuales con cURL

```bash
# Crear plan familiar
curl -X POST http://localhost:3001/api/mobile/family-plans \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Familia Test",
    "description": "Plan de prueba",
    "user_info": {
      "name": "Usuario Test",
      "phone": "+56912345678",
      "role": "LEADER"
    }
  }'

# Unirse a plan familiar (usar el family_code de la respuesta anterior)
curl -X POST http://localhost:3001/api/mobile/family-plans/join \
  -H "Content-Type: application/json" \
  -d '{
    "family_code": "CODIGO_OBTENIDO",
    "user_info": {
      "name": "Segundo Miembro",
      "role": "ADULT"
    }
  }'
```

## 📱 Integración con React Native

### Configuración del Cliente

```typescript
// config/api.ts
export const API_CONFIG = {
  baseURL: 'http://localhost:3001/api/mobile',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
};

// services/familyPlanAPI.ts
import AsyncStorage from '@react-native-async-storage/async-storage';

class FamilyPlanAPI {
  private baseURL = API_CONFIG.baseURL;
  
  async createFamilyPlan(data: CreateFamilyPlanDto) {
    const response = await fetch(`${this.baseURL}/family-plans`, {
      method: 'POST',
      headers: await this.getHeaders(),
      body: JSON.stringify(data)
    });
    return response.json();
  }
  
  private async getHeaders() {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json'
    };
    
    const token = await AsyncStorage.getItem('auth_token');
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }
    
    const deviceId = await AsyncStorage.getItem('device_id');
    if (deviceId) {
      headers['X-Device-ID'] = deviceId;
    }
    
    return headers;
  }
}
```

### Manejo de Estados Offline

```typescript
// hooks/useFamilyPlan.ts
import { useState, useEffect } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

export function useFamilyPlan(planId: string) {
  const [plan, setPlan] = useState(null);
  const [isOffline, setIsOffline] = useState(false);
  const [syncStatus, setSyncStatus] = useState('SYNCED');
  
  useEffect(() => {
    loadPlanData();
    setupSyncInterval();
  }, [planId]);
  
  const loadPlanData = async () => {
    // Cargar datos locales primero
    const localData = await AsyncStorage.getItem(`family_plan_${planId}`);
    if (localData) {
      setPlan(JSON.parse(localData));
    }
    
    // Intentar sincronizar con servidor
    try {
      const serverData = await familyPlanAPI.getFamilyPlan(planId);
      setPlan(serverData);
      await AsyncStorage.setItem(`family_plan_${planId}`, JSON.stringify(serverData));
      setIsOffline(false);
    } catch (error) {
      setIsOffline(true);
    }
  };
  
  const setupSyncInterval = () => {
    const interval = setInterval(async () => {
      if (!isOffline) {
        await syncPendingChanges();
      }
    }, 30000); // Sync cada 30 segundos
    
    return () => clearInterval(interval);
  };
}
```

## 🔒 Seguridad

### Políticas RLS Implementadas

- **Row Level Security** habilitado en todas las tablas
- **Acceso por membresía familiar** - solo miembros pueden ver datos
- **Roles diferenciados** - líderes tienen permisos adicionales
- **Auditoría completa** - todos los cambios se registran

### Validaciones

- **Entrada sanitizada** en todos los endpoints
- **Validación de tipos** con express-validator
- **Límites de rate limiting** específicos
- **Verificación de pertenencia** en cada operación

## 📊 Monitoreo y Logs

### Métricas Importantes

```bash
# Ver logs de planes familiares
tail -f logs/app.log | grep "family_plan"

# Monitorear sincronización
tail -f logs/app.log | grep "sync"

# Ver errores específicos
tail -f logs/error.log | grep "Family"
```

### Dashboard de Métricas

Las siguientes métricas están disponibles:

- Número de planes familiares activos
- Miembros por plan (promedio/máximo)
- Frecuencia de sincronización
- Conflictos de sincronización
- Tiempo de respuesta de endpoints

## 🚨 Troubleshooting

### Problemas Comunes

1. **Error de conexión a Supabase**
   ```bash
   # Verificar variables de entorno
   echo $SUPABASE_URL
   echo $SUPABASE_SERVICE_ROLE_KEY
   ```

2. **Tablas no encontradas**
   ```bash
   # Re-ejecutar migraciones
   psql -h <host> -U postgres -d postgres -f family_plans_schema.sql
   ```

3. **Errores de validación**
   ```bash
   # Verificar logs detallados
   DEBUG=volcano:* npm run dev
   ```

4. **Problemas de sincronización**
   ```bash
   # Limpiar datos de sync antiguos
   curl -X POST http://localhost:3001/api/mobile/family-plans/sync/cleanup
   ```

## 📞 Soporte

Para soporte técnico:

- **Documentación**: Ver `docs/FAMILY_PLANS_API.md`
- **Tests**: Ejecutar `npm test -- familyPlans`
- **Logs**: Revisar `logs/app.log` y `logs/error.log`
- **Base de datos**: Usar Supabase Dashboard para inspección directa

---

*Implementación completada el 2 de enero de 2025*
