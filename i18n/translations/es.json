{"app": {"name": "VolkanApp", "slogan": "Monitoreo volcánico en tiempo real"}, "navigation": {"home": "<PERSON><PERSON>o", "explore": "Explorar", "precursors": "Precursores", "familyPlan": "Plan Familiar"}, "common": {"loading": "Cargando...", "error": "Error", "retry": "Reintentar", "cancel": "<PERSON><PERSON><PERSON>", "ok": "OK", "yes": "Sí", "no": "No", "save": "Guardar", "edit": "<PERSON><PERSON>", "delete": "Eliminar", "close": "<PERSON><PERSON><PERSON>", "next": "Siguient<PERSON>", "previous": "Anterior", "continue": "<PERSON><PERSON><PERSON><PERSON>", "finish": "Terminar", "lessThanHour": "Hace menos de 1 hora", "hoursAgo": "Hace {{count}} horas", "daysAgo": "Hace {{count}} días"}, "alerts": {"levels": {"green": "Verde", "yellow": "Amarillo", "orange": "<PERSON><PERSON><PERSON>", "red": "<PERSON><PERSON><PERSON>", "purple": "Púrpura"}, "status": {"normal": "Normal", "advisory": "Precaución", "watch": "Vigilancia", "warning": "<PERSON><PERSON><PERSON>", "emergency": "Emergencia"}}, "home": {"title": "Volcano App", "subtitle": "Monitoreo volcánico en tiempo real", "lastUpdate": "Última actualización: {{time}}", "currentStatus": "Estado Actual", "quickActions": "Acciones Rápidas", "recentUpdates": "Actualizaciones Recientes", "emergency": "EMERGENCIA", "interactiveMap": "Mapa Interactivo", "precursors": "Precursores", "safetyGuides": "Guías de Seguridad", "officialNews": "Noticias Oficiales", "footer": {"data": "Datos oficiales de SERNAGEOMIN • ONEMI", "pullToRefresh": "Des<PERSON>za hacia abajo para actualizar"}}, "map": {"title": "Mapa de Seguridad Volcánica", "subtitle": "Volcán Villarrica • Tiempo real", "yourLocation": "Tu ubicación", "distanceToVolcano": "Distancia al volcán", "evacuation": "Evacuación", "tips": "Consejos"}, "precursors": {"title": "Análisis de Precursores", "subtitle": "Detección temprana • Volcán Villarrica", "dataExamples": "Ejemplos de Datos", "currentDataset": "Dataset Actual", "type": "Tipo", "unit": "Unidad", "dataPoints": "Puntos de Datos", "volcano": "Volcán", "howItWorks": "Cómo Funciona", "explanation": "Este sistema analiza la {{bold}}tasa de cambio{{/bold}} (primera derivada) y la {{bold}}aceleración{{/bold}} (segunda derivada) de los datos de precursores volcánicos.", "greenLevel": "Verde (≤ 1): Actividad estable", "yellowLevel": "Amarillo (1-5): Precaución, actividad acelerando", "redLevel": "Rojo (> 5): <PERSON><PERSON><PERSON>, aceleración peligrosa"}, "volcano": {"name": "<PERSON><PERSON><PERSON>", "location": "Pucón, Región de la Araucanía", "elevation": "2,847 metros", "status": {"loading": "Cargando estado del volcán...", "error": "Error al cargar el estado del volcán", "updated": "Actualizado: {{time}}"}}, "emergency": {"title": "Emergencia Volcánica", "question": "¿Necesita ayuda inmediata?", "call133": "Llamar 133 (<PERSON><PERSON><PERSON><PERSON><PERSON>)", "calling": "Llamando", "connecting": "Conectando con servicios de emergencia...", "evacuationRoutes": "Ver Rutas de Evacuación", "navigating": "Navegando", "openingRoutes": "Abriendo mapa de rutas de evacuación..."}, "evacuation": {"title": "Rutas de Evacuación", "description": "En caso de emergencia volcánica:", "primaryRoute": "Ruta Principal: <PERSON><PERSON> por Ruta 199", "alternativeRoute": "Ruta Alternativa: Hacia Villarrica por Ruta 199", "shelters": "Refugios: Centro de Pucón, Hospital, Escuelas", "fuelTip": "Mantén siempre el tanque de combustible lleno.", "understood": "Entendido"}, "safety": {"title": "Consejos de Seguridad", "locationMonitored": "Tu ubicación actual está siendo monitoreada", "stayAway": "Mantente alejado del volcán", "emergencyKit": "Ten preparado un kit de emergencia", "knowRoutes": "Conoce las rutas de evacuación", "stayInformed": "Mantente informado de las alertas oficiales", "distanceToVolcano": "Distancia al volcán: {{distance}} km"}, "connection": {"title": "Estado de Conexión", "apiBackend": "API Backend", "websocket": "WebSocket", "connected": "Conectado", "disconnected": "Desconectado", "lastUpdate": "Última actualización: {{time}}"}, "api": {"testPanel": "API Test Panel", "testIntegration": "Prueba la integración con el backend", "connectionStatus": "Estado de Conexiones", "apiHealth": "API Health", "websocket": "WebSocket", "loadedData": "<PERSON><PERSON> cargados", "testResults": "Resultados de Pruebas", "pressButtons": "Presiona los botones para probar la API"}, "language": {"title": "Idioma", "select": "Seleccionar idioma", "current": "Idioma actual", "change": "Cambiar idioma", "spanish": "Español", "english": "English", "haitianCreole": "<PERSON><PERSON><PERSON><PERSON>"}, "accessibility": {"emergency": "Botón de emergencia - Llama para ayuda inmediata", "emergencyHint": "Activa protocolos de emergencia y llama a servicios de emergencia", "evacuation": "Ver rutas de evacuación", "tips": "Ver consejos de seguridad"}, "auth": {"signIn": "<PERSON><PERSON><PERSON>", "signUp": "Registrarse", "login": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Accede a tu cuenta para gestionar tus planes familiares", "signIn": "<PERSON><PERSON><PERSON>", "signingIn": "Iniciando se<PERSON>...", "forgotPassword": "¿Olvidaste tu contraseña?", "noAccount": "¿No tienes cuenta?", "signUp": "<PERSON><PERSON><PERSON> cuenta"}, "register": {"title": "<PERSON><PERSON><PERSON>", "subtitle": "Únete para crear y gestionar planes familiares de emergencia", "createAccount": "<PERSON><PERSON><PERSON>", "creating": "<PERSON><PERSON>ndo cuenta...", "hasAccount": "¿Ya tienes cuenta?", "signIn": "In<PERSON><PERSON>", "passwordRequirements": "La contraseña debe tener al menos 6 caracteres", "success": {"title": "¡Cuenta creada!", "message": "Tu cuenta ha sido creada exitosamente. Ahora puedes crear y gestionar planes familiares."}}, "fields": {"email": "Email", "password": "Contraseña", "confirmPassword": "Con<PERSON><PERSON><PERSON>", "name": "Nombre completo", "phone": "Teléfono"}, "placeholders": {"email": "<EMAIL>", "password": "Tu contraseña", "confirmPassword": "Confirma tu contraseña", "name": "Tu nombre completo", "phone": "****** 567 8900"}, "error": {"title": "Error de Autenticación", "invalidForm": "Por favor completa todos los campos requeridos", "loginFailed": "Error al iniciar sesión", "registerFailed": "Error al crear cuenta", "networkError": "Error de conexión. Verifica tu internet."}, "forgotPassword": {"title": "Recup<PERSON>r <PERSON>", "comingSoon": "Esta función estará disponible pronto"}, "loading": {"initializing": "Inicializando autenticación..."}, "required": {"title": "Autenticación Requerida", "message": "Necesitas iniciar sesión para acceder a esta función", "signIn": "<PERSON><PERSON><PERSON>", "signUp": "<PERSON><PERSON><PERSON>"}}, "user": {"welcome": "¡Bienvenido!", "signInMessage": "Inicia sesión para acceder a todas las funciones", "anonymous": "Usuario", "noName": "Sin nombre"}, "profile": {"personalInfo": "Información Personal", "fields": {"name": "Nombre", "email": "Email", "phone": "Teléfono"}, "placeholders": {"name": "Tu nombre completo", "phone": "Tu número de teléfono"}, "notSet": "No configurado", "emailNote": "El email no se puede cambiar", "memberSince": "Miembro desde {{date}}", "saving": "Guardando...", "logout": {"title": "<PERSON><PERSON><PERSON>", "message": "¿Estás seguro que quieres cerrar sesión?", "confirm": "<PERSON><PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON>"}, "success": {"title": "Perfil Actualizado", "updated": "Tu perfil ha sido actualizado exitosamente"}, "error": {"title": "Error", "updateFailed": "<PERSON><PERSON>r al actualizar perfil", "networkError": "Error de conexión al actualizar perfil"}, "notAuthenticated": "No has iniciado sesión"}, "familyPlan": {"title": "Planes Familiares", "subtitle": "Gestiona tus planes de emergencia familiar", "primary": "Principal", "code": "Código", "empty": {"title": "Sin Planes Familiares", "message": "Crea un nuevo plan familiar o únete a uno existente para comenzar a prepararte para emergencias volcánicas."}, "actions": {"create": "Crear Plan", "join": "Unirse a Plan", "viewDetails": "<PERSON><PERSON>"}, "quickActions": {"title": "Acciones Rápidas", "create": "Nuevo Plan", "join": "Unirse"}, "stats": {"members": "Mi<PERSON><PERSON><PERSON>", "contacts": "Contactos", "kits": "Kits", "meetingPoints": "Punt<PERSON>", "membersCount": "{{count}} mi<PERSON><PERSON>s"}, "otherPlans": "Otros Planes"}}