# 👨‍👩‍👧‍👦 Family Emergency Plans API - Documentación para Desarrolladores Móviles

## 📋 Descripción General

La API de Planes Familiares de Emergencia permite a las familias crear, gestionar y sincronizar planes de emergencia volcánica. Está optimizada para aplicaciones móviles con soporte offline-first y sincronización automática.

## 🔗 Base URL

- **Desarrollo**: `http://localhost:3001/api/mobile/family-plans`
- **Producción**: `https://api.volcanoapp.com/api/mobile/family-plans`

## 🔐 Autenticación

La API soporta tanto usuarios autenticados como anónimos:

### Headers de Autenticación
```http
Authorization: Bearer <jwt-token>          # Opcional - para usuarios registrados
X-Device-ID: <device-uuid>                # Opcional - identificador del dispositivo
X-Anonymous-ID: <anonymous-uuid>          # Opcional - identificador anónimo
```

### Tipos de Usuario
- **Usuarios Registrados**: Tienen JWT token, pueden sincronizar entre dispositivos
- **Usuarios Anónimos**: Usan <PERSON> anónimo, datos locales al dispositivo

## 📱 Consideraciones para Móviles

### Optimizaciones
- **Compresión gzip** habilitada automáticamente
- **Respuestas compactas** con campos opcionales
- **Paginación automática** para listas grandes
- **Cache-friendly** con ETags y headers apropiados

### Soporte Offline
- **Datos críticos** disponibles sin conexión
- **Sincronización automática** al recuperar conexión
- **Resolución de conflictos** por timestamp
- **Estados de sincronización** claros

## 🏗️ Estructura de Datos

### Roles de Miembros Familiares
```typescript
enum FamilyMemberRole {
  LEADER = 'LEADER',      // Líder familiar (permisos completos)
  ADULT = 'ADULT',        // Adulto
  CHILD = 'CHILD',        // Menor de edad
  ELDERLY = 'ELDERLY',    // Adulto mayor
  DISABLED = 'DISABLED'   // Persona con discapacidad
}
```

### Categorías de Items de Kit
```typescript
enum EmergencyItemCategory {
  FOOD = 'FOOD',                    // Alimentos
  WATER = 'WATER',                  // Agua
  MEDICAL = 'MEDICAL',              // Medicamentos/primeros auxilios
  TOOLS = 'TOOLS',                  // Herramientas
  CLOTHING = 'CLOTHING',            // Ropa y abrigo
  DOCUMENTS = 'DOCUMENTS',          // Documentos importantes
  COMMUNICATION = 'COMMUNICATION',  // Comunicación
  OTHER = 'OTHER'                   // Otros
}
```

### Tipos de Puntos de Encuentro
```typescript
enum MeetingPointType {
  PRIMARY = 'PRIMARY',        // Punto principal
  SECONDARY = 'SECONDARY',    // Punto alternativo
  EVACUATION = 'EVACUATION',  // Punto de evacuación
  SHELTER = 'SHELTER'         // Refugio temporal
}
```

### Escenarios de Evacuación
```typescript
enum EvacuationScenario {
  NORMAL = 'NORMAL',        // Estado normal
  ADVISORY = 'ADVISORY',    // Alerta amarilla
  WATCH = 'WATCH',          // Alerta naranja
  WARNING = 'WARNING',      // Alerta roja
  EMERGENCY = 'EMERGENCY'   // Emergencia
}
```

## 🚀 Endpoints Principales

### 1. Crear Plan Familiar

```http
POST /api/mobile/family-plans
Content-Type: application/json

{
  "name": "Familia González",
  "description": "Plan de emergencia para familia de 4 personas",
  "user_info": {
    "name": "Carlos González",
    "phone": "+56912345678",
    "role": "LEADER"
  }
}
```

**Respuesta 201:**
```json
{
  "success": true,
  "data": {
    "id": "uuid-plan",
    "name": "Familia González",
    "family_code": "FAM123XY",
    "created_at": "2025-01-01T00:00:00Z",
    "member_count": 1,
    "sync_version": 1
  },
  "timestamp": "2025-01-01T00:00:00Z"
}
```

### 2. Unirse a Plan Familiar

```http
POST /api/mobile/family-plans/join
Content-Type: application/json

{
  "family_code": "FAM123XY",
  "user_info": {
    "name": "María González",
    "phone": "+56987654321",
    "role": "ADULT",
    "medical_info": {
      "blood_type": "A+",
      "allergies": ["penicilina"],
      "medications": ["levotiroxina"]
    },
    "special_needs": {
      "mobility": "normal",
      "assistance_needed": false
    }
  }
}
```

**Respuesta 200:**
```json
{
  "success": true,
  "data": {
    "family_plan": {
      "id": "uuid-plan",
      "name": "Familia González",
      "member_count": 2
    },
    "member_id": "uuid-member"
  },
  "timestamp": "2025-01-01T00:00:00Z"
}
```

### 3. Obtener Plan Completo

```http
GET /api/mobile/family-plans/uuid-plan?include=members,contacts,kits,meeting_points,evacuation_plans
Authorization: Bearer <token>
```

**Respuesta 200:**
```json
{
  "success": true,
  "data": {
    "plan": {
      "id": "uuid-plan",
      "name": "Familia González",
      "description": "Plan de emergencia...",
      "family_code": "FAM123XY",
      "created_at": "2025-01-01T00:00:00Z",
      "member_count": 2,
      "contact_count": 5,
      "kit_count": 2,
      "meeting_point_count": 3,
      "evacuation_plan_count": 4,
      "sync_version": 5
    },
    "members": [
      {
        "id": "uuid-member-1",
        "name": "Carlos González",
        "role": "LEADER",
        "phone": "+56912345678",
        "medical_info": {},
        "special_needs": {}
      }
    ],
    "contacts": [
      {
        "id": "uuid-contact-1",
        "name": "Bomberos Pucón",
        "phone": "+56452441234",
        "relationship": "emergency_service",
        "priority": 1,
        "is_local": true
      }
    ],
    "kits": [
      {
        "id": "uuid-kit-1",
        "name": "Kit Principal",
        "location": "Closet entrada",
        "last_checked": "2025-01-01T00:00:00Z",
        "total_items": 15,
        "checked_items": 12,
        "critical_items": 8,
        "expiring_soon": 2
      }
    ],
    "meeting_points": [
      {
        "id": "uuid-point-1",
        "name": "Plaza de Pucón",
        "address": "Plaza de Armas, Pucón",
        "point_type": "PRIMARY",
        "coordinates": {
          "lat": -39.2700,
          "lng": -71.9500
        },
        "accessibility_info": {
          "wheelchair_accessible": true,
          "parking_available": true
        }
      }
    ],
    "evacuation_plans": [
      {
        "id": "uuid-evacuation-1",
        "scenario": "WARNING",
        "plan_name": "Plan de Evacuación Parcial",
        "description": "Evacuación hacia zona de menor riesgo",
        "estimated_time": 30,
        "primary_route": {
          "description": "Casa → Ruta 199 → Villarrica",
          "distance": "18 km"
        }
      }
    ]
  },
  "timestamp": "2025-01-01T00:00:00Z"
}
```

### 4. Agregar Contacto de Emergencia

```http
POST /api/mobile/family-plans/uuid-plan/contacts
Content-Type: application/json
Authorization: Bearer <token>

{
  "name": "Bomberos Pucón",
  "relationship": "emergency_service",
  "phone": "+56452441234",
  "priority": 1,
  "is_local": true,
  "notes": "Servicio de bomberos local - disponible 24/7"
}
```

### 5. Agregar Kit de Emergencia

```http
POST /api/mobile/family-plans/uuid-plan/kits
Content-Type: application/json

{
  "name": "Kit Principal Casa",
  "location": "Closet del pasillo principal",
  "description": "Kit de emergencia principal",
  "notes": "Revisar mensualmente"
}
```

### 6. Agregar Item a Kit

```http
POST /api/mobile/family-plans/uuid-plan/kits/uuid-kit/items
Content-Type: application/json

{
  "category": "WATER",
  "item_name": "Agua embotellada",
  "quantity": 20,
  "unit": "litros",
  "expiry_date": "2026-12-31",
  "priority": "CRITICAL",
  "notes": "4 litros por persona para 5 días"
}
```

### 7. Agregar Punto de Encuentro

```http
POST /api/mobile/family-plans/uuid-plan/meeting-points
Content-Type: application/json

{
  "name": "Plaza de Armas de Pucón",
  "description": "Punto de encuentro principal en el centro",
  "address": "Plaza de Armas, O'Higgins con Fresia, Pucón",
  "coordinates": {
    "lat": -39.2700,
    "lng": -71.9500
  },
  "point_type": "PRIMARY",
  "accessibility_info": {
    "wheelchair_accessible": true,
    "parking_available": true,
    "lighting": "good"
  },
  "contact_info": {
    "emergency_phone": "133",
    "nearby_services": ["hospital", "carabineros", "bomberos"]
  }
}
```

### 8. Obtener Plan de Evacuación Actual

```http
GET /api/mobile/family-plans/uuid-plan/evacuation-plans/current?alert_level=WARNING
Authorization: Bearer <token>
```

**Respuesta 200:**
```json
{
  "success": true,
  "data": {
    "id": "uuid-evacuation-1",
    "scenario": "WARNING",
    "plan_name": "Plan de Evacuación Parcial",
    "description": "Evacuación hacia zona de menor riesgo",
    "primary_route": {
      "description": "Casa → Ruta 199 → Villarrica",
      "route": "Casa → Ruta 199 → Villarrica → Casa de Pedro",
      "distance": "18 km",
      "time": "25 minutos en auto"
    },
    "alternative_routes": [
      {
        "description": "Ruta por camino rural",
        "time": "35 minutos"
      }
    ],
    "transportation": {
      "primary": "vehículo familiar",
      "backup": "vehículo de vecinos"
    },
    "special_instructions": "Llevar ambos kits. Roberto va en asiento delantero. Llamar a Pedro antes de salir.",
    "estimated_time": 30
  },
  "timestamp": "2025-01-01T00:00:00Z"
}
```

## ⚠️ Manejo de Errores

### Códigos de Error Específicos

| Código | HTTP | Descripción | Acción Recomendada |
|--------|------|-------------|-------------------|
| `FAMILY_CODE_INVALID` | 422 | Código familiar inválido o expirado | Solicitar nuevo código |
| `FAMILY_NOT_FOUND` | 404 | Plan familiar no existe | Verificar ID del plan |
| `MEMBER_NOT_AUTHORIZED` | 403 | Usuario no es miembro de la familia | Unirse al plan familiar |
| `LEADER_REQUIRED` | 403 | Operación requiere ser líder familiar | Contactar al líder |
| `ALREADY_MEMBER` | 409 | Usuario ya es miembro | Continuar normalmente |
| `FAMILY_FULL` | 422 | Plan familiar lleno (máx. 20 miembros) | Crear nuevo plan |
| `SYNC_CONFLICT` | 409 | Conflicto de sincronización | Resolver conflictos |
| `OFFLINE_LIMIT_EXCEEDED` | 413 | Demasiados cambios offline | Sincronizar gradualmente |

### Ejemplo de Respuesta de Error

```json
{
  "success": false,
  "error": "FAMILY_CODE_INVALID",
  "message": "Invalid or expired family code",
  "timestamp": "2025-01-01T00:00:00Z"
}
```

## 🔄 Sincronización Offline

### Estrategia Offline-First

La API está diseñada para funcionar sin conexión:

1. **Almacenamiento Local**: Todos los datos se guardan localmente
2. **Sincronización Automática**: Al recuperar conexión
3. **Resolución de Conflictos**: Por timestamp más reciente
4. **Estados Claros**: Indicadores de estado de sincronización

### Endpoint de Sincronización

```http
POST /api/mobile/family-plans/uuid-plan/sync
Content-Type: application/json

{
  "last_sync": "2025-01-01T00:00:00Z",
  "device_info": {
    "device_id": "device-uuid",
    "platform": "iOS",
    "app_version": "1.0.0"
  },
  "changes": [
    {
      "table": "emergency_contacts",
      "action": "CREATE",
      "temp_id": "temp-1",
      "data": {
        "name": "Nuevo Contacto",
        "phone": "+56912345678",
        "relationship": "familiar",
        "priority": 5
      },
      "client_timestamp": "2025-01-01T01:00:00Z"
    },
    {
      "table": "emergency_kit_items",
      "action": "UPDATE",
      "id": "uuid-item",
      "data": {
        "is_checked": true
      },
      "client_timestamp": "2025-01-01T01:30:00Z"
    }
  ]
}
```

**Respuesta de Sincronización:**
```json
{
  "success": true,
  "data": {
    "sync_timestamp": "2025-01-01T02:00:00Z",
    "conflicts": [],
    "server_changes": [
      {
        "table": "emergency_contacts",
        "action": "UPDATE",
        "id": "uuid-contact",
        "data": {
          "priority": 2
        },
        "version": 3
      }
    ],
    "id_mappings": {
      "temp-1": "uuid-contact-real"
    }
  },
  "timestamp": "2025-01-01T02:00:00Z"
}
```

### Manejo de Conflictos

Cuando hay conflictos de sincronización:

```json
{
  "conflicts": [
    {
      "table": "emergency_contacts",
      "id": "uuid-contact",
      "client_version": 2,
      "server_version": 3,
      "client_data": {
        "name": "Contacto Editado Local",
        "priority": 5
      },
      "server_data": {
        "name": "Contacto Editado Servidor",
        "priority": 3
      }
    }
  ]
}
```

**Estrategias de Resolución:**
1. **Timestamp más reciente** (automático)
2. **Prioridad del servidor** (para datos críticos)
3. **Intervención manual** (para conflictos complejos)

## 📊 Estados de Sincronización

### Estados Posibles

```typescript
enum SyncStatus {
  SYNCED = 'SYNCED',                    // Sincronizado
  PENDING_UPLOAD = 'PENDING_UPLOAD',    // Cambios locales pendientes
  PENDING_DOWNLOAD = 'PENDING_DOWNLOAD', // Cambios remotos pendientes
  CONFLICT = 'CONFLICT'                  // Conflicto requiere resolución
}
```

### Verificar Estado de Sincronización

```http
GET /api/mobile/family-plans/uuid-plan/sync/status
Authorization: Bearer <token>
```

**Respuesta:**
```json
{
  "success": true,
  "data": {
    "sync_status": "SYNCED",
    "last_sync": "2025-01-01T02:00:00Z",
    "pending_changes_count": 0,
    "conflicts_count": 0,
    "sync_version": 15
  },
  "timestamp": "2025-01-01T02:00:00Z"
}
```

## 🔧 Configuración y Conexión

### Variables de Entorno

```bash
# Backend URL
REACT_NATIVE_API_BASE_URL=https://api.volcanoapp.com

# Configuración de sincronización
SYNC_INTERVAL_MINUTES=5
OFFLINE_STORAGE_LIMIT_MB=50
MAX_PENDING_CHANGES=100

# Configuración de autenticación
JWT_EXPIRY_HOURS=24
ANONYMOUS_SESSION_HOURS=72
```

### Inicialización en React Native

```typescript
import AsyncStorage from '@react-native-async-storage/async-storage';

// Configuración de la API
const API_CONFIG = {
  baseURL: process.env.REACT_NATIVE_API_BASE_URL,
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json'
  }
};

// Inicializar cliente
class FamilyPlanAPI {
  private baseURL: string;
  private deviceId: string;
  private anonymousId: string | null = null;
  private authToken: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    this.deviceId = this.generateDeviceId();
    this.loadStoredCredentials();
  }

  private async loadStoredCredentials() {
    this.authToken = await AsyncStorage.getItem('auth_token');
    this.anonymousId = await AsyncStorage.getItem('anonymous_id');

    if (!this.anonymousId) {
      this.anonymousId = this.generateAnonymousId();
      await AsyncStorage.setItem('anonymous_id', this.anonymousId);
    }
  }

  private getHeaders(): Record<string, string> {
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      'X-Device-ID': this.deviceId
    };

    if (this.authToken) {
      headers['Authorization'] = `Bearer ${this.authToken}`;
    } else if (this.anonymousId) {
      headers['X-Anonymous-ID'] = this.anonymousId;
    }

    return headers;
  }

  // Métodos de API...
  async createFamilyPlan(data: CreateFamilyPlanDto) {
    const response = await fetch(`${this.baseURL}/family-plans`, {
      method: 'POST',
      headers: this.getHeaders(),
      body: JSON.stringify(data)
    });

    return response.json();
  }
}
```

## 🧪 Testing y Desarrollo

### Datos de Prueba

Para testing, puedes usar estos datos de ejemplo:

```json
{
  "family_code": "TEST123X",
  "test_user": {
    "name": "Usuario de Prueba",
    "phone": "+56912345678",
    "role": "LEADER"
  }
}
```

### Endpoints de Desarrollo

```http
# Información de la API
GET /api/mobile/family-plans/_info

# Estado del servidor
GET /api/mobile/_health
```

## 📞 Soporte y Contacto

- **Documentación**: [docs.volcanoapp.com](https://docs.volcanoapp.com)
- **Issues**: [github.com/volcanoapp/issues](https://github.com/volcanoapp/issues)
- **Email**: <EMAIL>

---

*Última actualización: 2 de enero de 2025*
