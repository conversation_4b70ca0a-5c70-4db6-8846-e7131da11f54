/**
 * VolcanoStatus Component
 * Displays current volcano alert level with clear visual indicators
 */

import React from 'react';
import { View, StyleSheet } from 'react-native';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Colors, AlertLevel, AlertLevels } from '@/constants/Colors';
import { Layout, Spacing, BorderRadius } from '@/constants/Layout';
import { AccessibleText, Heading2, BodyText, Caption } from './ui/AccessibleText';
import { useCurrentAlert } from '@/hooks/useApi';

export interface VolcanoStatusProps {
  alertLevel: AlertLevel;
  volcanoName: string;
  lastUpdate: Date;
  description?: string;
  compact?: boolean;
}

export function VolcanoStatus({
  alertLevel,
  volcanoName,
  lastUpdate,
  description,
  compact = false,
}: VolcanoStatusProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  
  // Get alert level info
  const getAlertInfo = (level: AlertLevel) => {
    const alertColor = AlertLevels[level];
    const colorKey = `alert${alertColor.charAt(0).toUpperCase() + alertColor.slice(1)}` as keyof typeof colors;
    
    const alertMessages = {
      NORMAL: {
        title: 'Normal',
        message: 'Actividad volcánica normal',
        icon: '🟢',
        urgency: 'low',
      },
      ADVISORY: {
        title: 'Aviso',
        message: 'Cambios detectados en la actividad',
        icon: '🟡',
        urgency: 'medium',
      },
      WATCH: {
        title: 'Vigilancia',
        message: 'Actividad volcánica elevada',
        icon: '🟠',
        urgency: 'medium',
      },
      WARNING: {
        title: 'Alerta',
        message: 'Erupción probable en 24 horas',
        icon: '🔴',
        urgency: 'high',
      },
      EMERGENCY: {
        title: 'Emergencia',
        message: 'Erupción en curso o inminente',
        icon: '🟣',
        urgency: 'critical',
      },
    };
    
    return {
      ...alertMessages[level],
      color: colors[colorKey],
    };
  };
  
  const alertInfo = getAlertInfo(alertLevel);
  
  // Format last update time
  const formatLastUpdate = (date: Date) => {
    const now = new Date();
    const diffMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60));
    
    if (diffMinutes < 1) return 'Hace menos de 1 minuto';
    if (diffMinutes < 60) return `Hace ${diffMinutes} minutos`;
    
    const diffHours = Math.floor(diffMinutes / 60);
    if (diffHours < 24) return `Hace ${diffHours} horas`;
    
    const diffDays = Math.floor(diffHours / 24);
    return `Hace ${diffDays} días`;
  };
  
  const styles = StyleSheet.create({
    container: {
      backgroundColor: colors.background,
      borderRadius: BorderRadius.lg,
      padding: compact ? Spacing.md : Spacing.lg,
      borderLeftWidth: 6,
      borderLeftColor: alertInfo.color,
      ...Layout.card,
    },
    
    header: {
      flexDirection: 'row',
      alignItems: 'center',
      marginBottom: compact ? Spacing.sm : Spacing.md,
    },
    
    alertIcon: {
      fontSize: compact ? 24 : 32,
      marginRight: Spacing.sm,
    },
    
    alertBadge: {
      backgroundColor: alertInfo.color,
      paddingHorizontal: Spacing.sm,
      paddingVertical: 4,
      borderRadius: BorderRadius.sm,
      marginLeft: 'auto',
    },
    
    volcanoName: {
      flex: 1,
      marginRight: Spacing.sm,
    },
    
    statusInfo: {
      marginBottom: compact ? Spacing.sm : Spacing.md,
    },
    
    description: {
      marginTop: Spacing.sm,
    },
    
    footer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      marginTop: Spacing.sm,
      paddingTop: Spacing.sm,
      borderTopWidth: 1,
      borderTopColor: colors.border,
    },
    
    updateTime: {
      flex: 1,
    },
    
    urgencyIndicator: {
      paddingHorizontal: Spacing.sm,
      paddingVertical: 2,
      borderRadius: BorderRadius.sm,
      backgroundColor: colors.backgroundSecondary,
    },
  });
  
  return (
    <View 
      style={styles.container}
      accessible={true}
      accessibilityRole="summary"
      accessibilityLabel={`Estado del volcán ${volcanoName}: ${alertInfo.title}. ${alertInfo.message}. Última actualización: ${formatLastUpdate(lastUpdate)}`}
    >
      {/* Header with volcano name and alert level */}
      <View style={styles.header}>
        <AccessibleText style={styles.alertIcon}>
          {alertInfo.icon}
        </AccessibleText>
        
        <View style={styles.volcanoName}>
          <AccessibleText variant={compact ? "h4" : "h2"}>
          {volcanoName}
        </AccessibleText>
        </View>
        
        <View style={styles.alertBadge}>
          <AccessibleText 
            variant={compact ? "bodySmall" : "button"}
            color="inverse"
          >
            {alertInfo.title}
          </AccessibleText>
        </View>
      </View>
      
      {/* Status information */}
      <View style={styles.statusInfo}>
        <AccessibleText 
          variant={compact ? "body" : "bodyLarge"}
          color="secondary"
        >
          {alertInfo.message}
        </AccessibleText>
        
        {description && !compact && (
          <View style={styles.description}>
            <AccessibleText variant="body" color="muted">{description}</AccessibleText>
          </View>
        )}
      </View>
      
      {/* Footer with update time and urgency */}
      <View style={styles.footer}>
        <View style={styles.updateTime}>
          <AccessibleText variant="caption" color="muted">
            Actualizado: {formatLastUpdate(lastUpdate)}
          </AccessibleText>
        </View>
        
        {alertInfo.urgency !== 'low' && (
          <View style={styles.urgencyIndicator}>
            <AccessibleText variant="caption" color="secondary">
              {alertInfo.urgency === 'critical' ? 'CRÍTICO' :
               alertInfo.urgency === 'high' ? 'ALTO' : 'MEDIO'}
            </AccessibleText>
          </View>
        )}
      </View>
    </View>
  );
}

// Quick status indicator for compact spaces
export function VolcanoStatusIndicator({
  alertLevel,
  size = 'medium'
}: {
  alertLevel: AlertLevel;
  size?: 'small' | 'medium' | 'large';
}) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];
  const alertColor = AlertLevels[alertLevel];
  const colorKey = `alert${alertColor.charAt(0).toUpperCase() + alertColor.slice(1)}` as keyof typeof colors;

  const sizeMap = {
    small: 12,
    medium: 16,
    large: 24,
  };

  return (
    <View
      style={{
        width: sizeMap[size],
        height: sizeMap[size],
        borderRadius: sizeMap[size] / 2,
        backgroundColor: colors[colorKey],
      }}
      accessible={true}
      accessibilityLabel={`Nivel de alerta: ${alertLevel}`}
    />
  );
}

// Component that fetches real data from API
export function LiveVolcanoStatus({ compact = false }: { compact?: boolean }) {
  const { data: alert, isLoading, isError, refetch } = useCurrentAlert();
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  if (isLoading) {
    return (
      <View style={{
        backgroundColor: colors.background,
        borderRadius: BorderRadius.lg,
        padding: compact ? Spacing.md : Spacing.lg,
        ...Layout.card,
      }}>
        <AccessibleText variant="body" color="secondary">
          Cargando estado del volcán...
        </AccessibleText>
      </View>
    );
  }

  if (isError || !alert) {
    return (
      <View style={{
        backgroundColor: colors.background,
        borderRadius: BorderRadius.lg,
        padding: compact ? Spacing.md : Spacing.lg,
        borderLeftWidth: 6,
        borderLeftColor: colors.alertRed,
        ...Layout.card,
      }}>
        <AccessibleText variant="body" color="error">
          Error al cargar el estado del volcán
        </AccessibleText>
        <AccessibleText
          variant="button"
          color="primary"
          style={{ marginTop: Spacing.sm }}
          onPress={() => refetch()}
        >
          Reintentar
        </AccessibleText>
      </View>
    );
  }

  return (
    <VolcanoStatus
      alertLevel={alert.alert_level}
      volcanoName={alert.volcano_name}
      lastUpdate={new Date(alert.created_at)}
      description={alert.message}
      compact={compact}
    />
  );
}
