/**
 * 🌋 Volcano App Mobile - Auth Navigator
 * Componente para manejar navegación condicional basada en autenticación
 */

import React, { useEffect } from 'react';
import { View, StyleSheet } from 'react-native';
import { router, useSegments } from 'expo-router';

import { useAuth } from '@/hooks/useAuth';
import { AuthGuard } from './AuthGuard';

// =====================================================
// CONFIGURACIÓN DE RUTAS
// =====================================================

// Rutas que requieren autenticación
const PROTECTED_ROUTES = [
  '/(tabs)',
  '/family-plan',
  '/profile',
  '/settings',
] as const;

// Rutas de autenticación
const AUTH_ROUTES = [
  '/(auth)',
  '/(auth)/login',
  '/(auth)/register',
] as const;

// Rutas públicas (no requieren autenticación)
const PUBLIC_ROUTES = [
  '/onboarding',
  '/about',
  '/help',
] as const;

// =====================================================
// COMPONENTE PRINCIPAL
// =====================================================

interface AuthNavigatorProps {
  children: React.ReactNode;
}

export function AuthNavigator({ children }: AuthNavigatorProps) {
  const { isAuthenticated, isInitializing } = useAuth();
  const segments = useSegments();

  useEffect(() => {
    if (isInitializing) return; // Esperar a que termine la inicialización

    const currentPath = `/${segments.join('/')}`;
    console.log('🔐 AuthNavigator: Current path:', currentPath);
    console.log('🔐 AuthNavigator: Is authenticated:', isAuthenticated);

    // Determinar si la ruta actual requiere autenticación
    const isProtectedRoute = PROTECTED_ROUTES.some(route => 
      currentPath.startsWith(route)
    );
    
    const isAuthRoute = AUTH_ROUTES.some(route => 
      currentPath.startsWith(route)
    );

    const isPublicRoute = PUBLIC_ROUTES.some(route => 
      currentPath.startsWith(route)
    );

    // Lógica de redirección
    if (isAuthenticated) {
      // Usuario autenticado
      if (isAuthRoute) {
        // Si está en una ruta de auth, redirigir a la app principal
        console.log('🔐 AuthNavigator: Redirecting authenticated user to main app');
        router.replace('/(tabs)');
      }
      // Si está en una ruta protegida o pública, permitir acceso
    } else {
      // Usuario no autenticado
      if (isProtectedRoute) {
        // Si intenta acceder a una ruta protegida, redirigir a login
        console.log('🔐 AuthNavigator: Redirecting unauthenticated user to login');
        router.replace('/(auth)/login');
      }
      // Si está en una ruta de auth o pública, permitir acceso
    }
  }, [isAuthenticated, isInitializing, segments]);

  // Durante la inicialización, mostrar el AuthGuard que maneja el loading
  if (isInitializing) {
    return (
      <AuthGuard requireAuth={false}>
        {children}
      </AuthGuard>
    );
  }

  return <>{children}</>;
}

// =====================================================
// HOOKS PERSONALIZADOS
// =====================================================

/**
 * Hook para obtener información sobre la ruta actual
 */
export function useCurrentRoute() {
  const segments = useSegments();
  const { isAuthenticated, isInitializing } = useAuth();
  
  const currentPath = `/${segments.join('/')}`;
  
  const isProtectedRoute = PROTECTED_ROUTES.some(route => 
    currentPath.startsWith(route)
  );
  
  const isAuthRoute = AUTH_ROUTES.some(route => 
    currentPath.startsWith(route)
  );
  
  const isPublicRoute = PUBLIC_ROUTES.some(route => 
    currentPath.startsWith(route)
  );

  return {
    currentPath,
    segments,
    isProtectedRoute,
    isAuthRoute,
    isPublicRoute,
    canAccess: isPublicRoute || 
               (isProtectedRoute && isAuthenticated) || 
               (isAuthRoute && !isAuthenticated),
    shouldRedirect: !isInitializing && (
      (isProtectedRoute && !isAuthenticated) ||
      (isAuthRoute && isAuthenticated)
    ),
  };
}

/**
 * Hook para navegación segura con autenticación
 */
export function useAuthNavigation() {
  const { isAuthenticated, isInitializing } = useAuth();

  const navigateToProtected = (path: string) => {
    if (isAuthenticated) {
      router.push(path as any);
    } else {
      router.push('/(auth)/login');
    }
  };

  const navigateToAuth = (path: '/(auth)/login' | '/(auth)/register') => {
    if (!isAuthenticated) {
      router.push(path);
    } else {
      router.push('/(tabs)');
    }
  };

  const navigateToPublic = (path: string) => {
    router.push(path as any);
  };

  const navigateBasedOnAuth = (
    authenticatedPath: string,
    unauthenticatedPath: string = '/(auth)/login'
  ) => {
    if (isAuthenticated) {
      router.push(authenticatedPath as any);
    } else {
      router.push(unauthenticatedPath as any);
    }
  };

  return {
    isAuthenticated,
    isInitializing,
    navigateToProtected,
    navigateToAuth,
    navigateToPublic,
    navigateBasedOnAuth,
  };
}

/**
 * Hook para verificar permisos de ruta
 */
export function useRoutePermissions() {
  const { isAuthenticated, isInitializing, user } = useAuth();
  const { currentPath, isProtectedRoute, isAuthRoute, isPublicRoute } = useCurrentRoute();

  const canAccessCurrentRoute = () => {
    if (isInitializing) return true; // Permitir durante inicialización
    
    if (isPublicRoute) return true;
    if (isAuthRoute) return !isAuthenticated;
    if (isProtectedRoute) return isAuthenticated;
    
    return true; // Por defecto permitir acceso
  };

  const getRedirectPath = () => {
    if (isProtectedRoute && !isAuthenticated) {
      return '/(auth)/login';
    }
    
    if (isAuthRoute && isAuthenticated) {
      return '/(tabs)';
    }
    
    return null;
  };

  return {
    currentPath,
    canAccess: canAccessCurrentRoute(),
    redirectPath: getRedirectPath(),
    isProtectedRoute,
    isAuthRoute,
    isPublicRoute,
    user,
  };
}

// =====================================================
// UTILIDADES
// =====================================================

/**
 * Verificar si una ruta requiere autenticación
 */
export function isProtectedRoute(path: string): boolean {
  return PROTECTED_ROUTES.some(route => path.startsWith(route));
}

/**
 * Verificar si una ruta es de autenticación
 */
export function isAuthRoute(path: string): boolean {
  return AUTH_ROUTES.some(route => path.startsWith(route));
}

/**
 * Verificar si una ruta es pública
 */
export function isPublicRoute(path: string): boolean {
  return PUBLIC_ROUTES.some(route => path.startsWith(route));
}

/**
 * Obtener la ruta de redirección apropiada
 */
export function getRedirectRoute(isAuthenticated: boolean, currentPath: string): string | null {
  if (isProtectedRoute(currentPath) && !isAuthenticated) {
    return '/(auth)/login';
  }
  
  if (isAuthRoute(currentPath) && isAuthenticated) {
    return '/(tabs)';
  }
  
  return null;
}
