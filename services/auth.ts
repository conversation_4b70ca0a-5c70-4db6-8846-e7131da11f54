/**
 * 🌋 Volcano App Mobile - Authentication Service
 * Servicio de autenticación con Supabase para la aplicación móvil
 */

import { supabase } from './supabase';
import { Session, User, AuthError } from '@supabase/supabase-js';
import AsyncStorage from '@react-native-async-storage/async-storage';

// =====================================================
// TIPOS DE AUTENTICACIÓN
// =====================================================

export interface AuthUser {
  id: string;
  email: string;
  name?: string;
  avatar_url?: string;
  phone?: string;
  created_at: string;
  updated_at: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  name: string;
  phone?: string;
}

export interface AuthState {
  user: AuthUser | null;
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
}

export interface AuthResponse {
  success: boolean;
  user?: AuthUser;
  session?: Session;
  error?: string;
}

// =====================================================
// CONSTANTES
// =====================================================

const AUTH_STORAGE_KEYS = {
  SESSION: 'volcano_auth_session',
  USER: 'volcano_auth_user',
  DEVICE_ID: 'volcano_device_id',
} as const;

// =====================================================
// SERVICIO DE AUTENTICACIÓN
// =====================================================

class AuthService {
  private currentUser: AuthUser | null = null;
  private currentSession: Session | null = null;
  private isInitialized = false;

  /**
   * Inicializar el servicio de autenticación
   */
  async initialize(): Promise<AuthState> {
    try {
      console.log('🔐 Initializing Auth Service...');

      // Obtener sesión actual de Supabase
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.error('❌ Error getting session:', error);
        return this.getEmptyAuthState();
      }

      if (session?.user) {
        // Obtener datos completos del usuario
        const userData = await this.getUserData(session.user.id);
        
        this.currentSession = session;
        this.currentUser = userData;
        
        // Guardar en storage local
        await this.saveToStorage(session, userData);
        
        console.log('✅ User authenticated:', userData.email);
        this.isInitialized = true;
        
        return {
          user: userData,
          session: session,
          isLoading: false,
          isAuthenticated: true,
        };
      }

      // No hay sesión activa
      console.log('ℹ️ No active session found');
      this.isInitialized = true;
      
      return this.getEmptyAuthState();
    } catch (error) {
      console.error('❌ Error initializing auth:', error);
      this.isInitialized = true;
      return this.getEmptyAuthState();
    }
  }

  /**
   * Registrar nuevo usuario
   */
  async register(credentials: RegisterCredentials): Promise<AuthResponse> {
    try {
      console.log('🔐 Registering user:', credentials.email);

      const { data, error } = await supabase.auth.signUp({
        email: credentials.email,
        password: credentials.password,
        options: {
          data: {
            name: credentials.name,
            phone: credentials.phone,
          },
        },
      });

      if (error) {
        console.error('❌ Registration error:', error);
        return {
          success: false,
          error: this.getErrorMessage(error),
        };
      }

      if (data.user && data.session) {
        // Crear perfil de usuario
        const userData = await this.createUserProfile(data.user, credentials);
        
        this.currentUser = userData;
        this.currentSession = data.session;
        
        await this.saveToStorage(data.session, userData);
        
        console.log('✅ User registered successfully:', userData.email);
        
        return {
          success: true,
          user: userData,
          session: data.session,
        };
      }

      return {
        success: false,
        error: 'Error creating user account',
      };
    } catch (error) {
      console.error('❌ Registration error:', error);
      return {
        success: false,
        error: 'Network error during registration',
      };
    }
  }

  /**
   * Iniciar sesión
   */
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    try {
      console.log('🔐 Logging in user:', credentials.email);

      const { data, error } = await supabase.auth.signInWithPassword({
        email: credentials.email,
        password: credentials.password,
      });

      if (error) {
        console.error('❌ Login error:', error);
        return {
          success: false,
          error: this.getErrorMessage(error),
        };
      }

      if (data.user && data.session) {
        const userData = await this.getUserData(data.user.id);
        
        this.currentUser = userData;
        this.currentSession = data.session;
        
        await this.saveToStorage(data.session, userData);
        
        console.log('✅ User logged in successfully:', userData.email);
        
        return {
          success: true,
          user: userData,
          session: data.session,
        };
      }

      return {
        success: false,
        error: 'Invalid login credentials',
      };
    } catch (error) {
      console.error('❌ Login error:', error);
      return {
        success: false,
        error: 'Network error during login',
      };
    }
  }

  /**
   * Cerrar sesión
   */
  async logout(): Promise<boolean> {
    try {
      console.log('🔐 Logging out user...');

      const { error } = await supabase.auth.signOut();
      
      if (error) {
        console.error('❌ Logout error:', error);
        return false;
      }

      // Limpiar estado local
      this.currentUser = null;
      this.currentSession = null;
      
      // Limpiar storage
      await this.clearStorage();
      
      console.log('✅ User logged out successfully');
      return true;
    } catch (error) {
      console.error('❌ Logout error:', error);
      return false;
    }
  }

  /**
   * Obtener usuario actual
   */
  getCurrentUser(): AuthUser | null {
    return this.currentUser;
  }

  /**
   * Obtener sesión actual
   */
  getCurrentSession(): Session | null {
    return this.currentSession;
  }

  /**
   * Verificar si está autenticado
   */
  isAuthenticated(): boolean {
    return !!(this.currentUser && this.currentSession);
  }

  /**
   * Verificar si está inicializado
   */
  isReady(): boolean {
    return this.isInitialized;
  }

  // =====================================================
  // MÉTODOS PRIVADOS
  // =====================================================

  private async getUserData(userId: string): Promise<AuthUser> {
    try {
      const { data, error } = await supabase
        .from('user_profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error || !data) {
        // Si no existe perfil, crear uno básico
        const { data: user } = await supabase.auth.getUser();
        if (user.user) {
          return await this.createUserProfile(user.user, {
            email: user.user.email!,
            password: '',
            name: user.user.user_metadata?.name || 'Usuario',
            phone: user.user.user_metadata?.phone,
          });
        }
        throw new Error('User not found');
      }

      return {
        id: data.id,
        email: data.email,
        name: data.name,
        avatar_url: data.avatar_url,
        phone: data.phone,
        created_at: data.created_at,
        updated_at: data.updated_at,
      };
    } catch (error) {
      console.error('❌ Error getting user data:', error);
      throw error;
    }
  }

  private async createUserProfile(user: User, credentials: RegisterCredentials): Promise<AuthUser> {
    try {
      const profileData = {
        id: user.id,
        email: user.email!,
        name: credentials.name,
        phone: credentials.phone,
        avatar_url: null,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      const { error } = await supabase
        .from('user_profiles')
        .insert(profileData);

      if (error) {
        console.error('❌ Error creating user profile:', error);
        // Si falla la creación del perfil, devolver datos básicos
        return profileData;
      }

      return profileData;
    } catch (error) {
      console.error('❌ Error creating user profile:', error);
      throw error;
    }
  }

  private async saveToStorage(session: Session, user: AuthUser): Promise<void> {
    try {
      await AsyncStorage.setItem(AUTH_STORAGE_KEYS.SESSION, JSON.stringify(session));
      await AsyncStorage.setItem(AUTH_STORAGE_KEYS.USER, JSON.stringify(user));
    } catch (error) {
      console.error('❌ Error saving to storage:', error);
    }
  }

  private async clearStorage(): Promise<void> {
    try {
      await AsyncStorage.multiRemove([
        AUTH_STORAGE_KEYS.SESSION,
        AUTH_STORAGE_KEYS.USER,
      ]);
    } catch (error) {
      console.error('❌ Error clearing storage:', error);
    }
  }

  private getEmptyAuthState(): AuthState {
    return {
      user: null,
      session: null,
      isLoading: false,
      isAuthenticated: false,
    };
  }

  private getErrorMessage(error: AuthError): string {
    switch (error.message) {
      case 'Invalid login credentials':
        return 'Email o contraseña incorrectos';
      case 'User already registered':
        return 'Este email ya está registrado';
      case 'Password should be at least 6 characters':
        return 'La contraseña debe tener al menos 6 caracteres';
      case 'Invalid email':
        return 'Email inválido';
      default:
        return error.message || 'Error de autenticación';
    }
  }
}

// =====================================================
// INSTANCIA SINGLETON
// =====================================================

export const authService = new AuthService();

// =====================================================
// FUNCIONES DE UTILIDAD
// =====================================================

/**
 * Obtener token de autenticación actual
 */
export async function getAuthToken(): Promise<string | null> {
  try {
    const session = authService.getCurrentSession();
    return session?.access_token || null;
  } catch (error) {
    console.error('❌ Error getting auth token:', error);
    return null;
  }
}

/**
 * Verificar si el usuario está autenticado
 */
export function isUserAuthenticated(): boolean {
  return authService.isAuthenticated();
}

/**
 * Obtener ID del usuario actual
 */
export function getCurrentUserId(): string | null {
  const user = authService.getCurrentUser();
  return user?.id || null;
}
