/**
 * Internationalization Configuration
 * Sistema multiidioma para VolkanApp
 * Soporte: <PERSON>sp<PERSON>, English, Kreyò<PERSON> (Haitian Creole)
 */

import i18n from 'i18next';
import { initReactI18next } from 'react-i18next';
import * as Localization from 'expo-localization';
import AsyncStorage from '@react-native-async-storage/async-storage';

// Import translations
import es from './translations/es.json';
import en from './translations/en.json';
import ht from './translations/ht.json';

// Language detection and persistence
const LANGUAGE_STORAGE_KEY = 'app-language';

const languageDetector = {
  type: 'languageDetector' as const,
  async: true,
  detect: async (callback: (lang: string) => void) => {
    try {
      // Try to get stored language first
      const storedLanguage = await AsyncStorage.getItem(LANGUAGE_STORAGE_KEY);
      if (storedLanguage) {
        callback(storedLanguage);
        return;
      }
      
      // Fall back to device locale
      const deviceLocale = Localization.locale;
      let detectedLanguage = 'es'; // Default to Spanish
      
      if (deviceLocale.startsWith('en')) {
        detectedLanguage = 'en';
      } else if (deviceLocale.startsWith('ht')) {
        detectedLanguage = 'ht';
      } else if (deviceLocale.startsWith('es')) {
        detectedLanguage = 'es';
      }
      
      callback(detectedLanguage);
    } catch (error) {
      console.warn('Error detecting language:', error);
      callback('es'); // Fallback to Spanish
    }
  },
  init: () => {},
  cacheUserLanguage: async (language: string) => {
    try {
      await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, language);
    } catch (error) {
      console.warn('Error saving language:', error);
    }
  },
};

// i18n configuration
i18n
  .use(languageDetector)
  .use(initReactI18next)
  .init({
    compatibilityJSON: 'v3', // Important for React Native
    resources: {
      es: { translation: es },
      en: { translation: en },
      ht: { translation: ht },
    },
    fallbackLng: 'es', // Spanish as fallback
    debug: __DEV__, // Enable debug in development
    
    interpolation: {
      escapeValue: false, // React already does escaping
    },
    
    react: {
      useSuspense: false, // Important for React Native
    },
  });

export default i18n;

// Helper functions for language management
export const getSupportedLanguages = () => [
  { code: 'es', name: 'Español', nativeName: 'Español' },
  { code: 'en', name: 'English', nativeName: 'English' },
  { code: 'ht', name: 'Haitian Creole', nativeName: 'Kreyòl Ayisyen' },
];

export const changeLanguage = async (languageCode: string) => {
  try {
    await i18n.changeLanguage(languageCode);
    await AsyncStorage.setItem(LANGUAGE_STORAGE_KEY, languageCode);
  } catch (error) {
    console.error('Error changing language:', error);
  }
};

export const getCurrentLanguage = () => i18n.language || 'es';

export const isRTL = () => {
  // Add RTL language codes here if needed in the future
  const rtlLanguages = [];
  return rtlLanguages.includes(getCurrentLanguage());
};